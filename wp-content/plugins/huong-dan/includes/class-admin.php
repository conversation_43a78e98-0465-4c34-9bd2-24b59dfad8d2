<?php
/**
 * Admin functionality for Huong <PERSON> plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_huong_dan_update_menu_order', array($this, 'ajax_update_menu_order'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Hướng dẫn SePay',
            'Hướng dẫn',
            'manage_options',
            'huong-dan',
            array($this, 'admin_page'),
            'dashicons-book-alt',
            30
        );
        
        // Add menu management submenu
        add_submenu_page(
            'huong-dan',
            'Quản lý Menu',
            'Quản lý Menu',
            'manage_options',
            'huong-dan-menu',
            array($this, 'menu_manager')
        );

        // Add submenu pages
        $pages = HuongDan_Database::get_all_pages();
        foreach ($pages as $page) {
            if ($page->has_link) { // Only add submenu for pages with links
                add_submenu_page(
                    'huong-dan',
                    $page->title,
                    $page->title,
                    'manage_options',
                    'huong-dan-' . $page->slug,
                    array($this, 'edit_page')
                );
            }
        }

        // Add new page submenu
        add_submenu_page(
            'huong-dan',
            'Thêm trang mới',
            'Thêm trang mới',
            'manage_options',
            'huong-dan-new',
            array($this, 'new_page')
        );
    }
    
    public function admin_init() {
        // Register settings
        register_setting('huong_dan_settings', 'huong_dan_options');
        
        // Handle form submissions
        if (isset($_POST['huong_dan_save'])) {
            $this->save_page();
        }
        
        if (isset($_POST['huong_dan_delete'])) {
            $this->delete_page();
        }
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'huong-dan') !== false) {
            wp_enqueue_editor();
            wp_enqueue_script('jquery');
            wp_enqueue_script('jquery-ui-sortable');

            // Enqueue custom admin script for menu management
            wp_enqueue_script(
                'huong-dan-admin',
                HUONG_DAN_PLUGIN_URL . 'assets/admin.js',
                array('jquery', 'jquery-ui-sortable'),
                HUONG_DAN_VERSION,
                true
            );

            // Localize script for AJAX
            wp_localize_script('huong-dan-admin', 'huongDanAjax', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('huong_dan_menu_nonce')
            ));

            // Enqueue admin CSS
            wp_enqueue_style(
                'huong-dan-admin',
                HUONG_DAN_PLUGIN_URL . 'assets/admin.css',
                array(),
                HUONG_DAN_VERSION
            );
        }
    }
    
    public function admin_page() {
        $pages = HuongDan_Database::get_all_pages();
        ?>
        <div class="wrap">
            <h1>Quản lý Hướng dẫn SePay</h1>
            
            <div class="card">
                <h2>Danh sách trang hướng dẫn</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Tiêu đề</th>
                            <th>Slug</th>
                            <th>Thứ tự</th>
                            <th>Ngày tạo</th>
                            <th>Hành động</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($pages as $page): ?>
                        <tr>
                            <td><strong><?php echo esc_html($page->title); ?></strong></td>
                            <td><?php echo esc_html($page->slug); ?></td>
                            <td><?php echo esc_html($page->menu_order); ?></td>
                            <td><?php echo esc_html($page->created_at); ?></td>
                            <td>
                                <a href="<?php echo admin_url('admin.php?page=huong-dan-' . $page->slug); ?>" class="button">Chỉnh sửa</a>
                                <a href="<?php echo home_url('/huong-dan/' . $page->slug); ?>" class="button" target="_blank">Xem</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <p>
                <a href="<?php echo admin_url('admin.php?page=huong-dan-new'); ?>" class="button button-primary">Thêm trang mới</a>
            </p>
        </div>
        <?php
    }
    
    public function edit_page() {
        $slug = str_replace('huong-dan-', '', $_GET['page']);
        $page = HuongDan_Database::get_page($slug);
        
        if (!$page) {
            echo '<div class="wrap"><h1>Không tìm thấy trang</h1></div>';
            return;
        }
        
        $this->render_page_form($page);
    }
    
    public function new_page() {
        $page = (object) array(
            'id' => 0,
            'slug' => '',
            'title' => '',
            'content' => '',
            'parent_id' => 0,
            'menu_order' => 0,
            'depth' => 0,
            'is_parent' => 0,
            'has_link' => 1,
            'icon' => ''
        );

        $this->render_page_form($page, true);
    }

    public function menu_manager() {
        $hierarchical_pages = HuongDan_Database::get_hierarchical_pages();
        ?>
        <div class="wrap">
            <h1>Quản lý Menu Hướng dẫn</h1>
            <p>Kéo thả để sắp xếp lại thứ tự menu. Các mục cha (không có link) sẽ hiển thị như tiêu đề phân nhóm.</p>

            <div class="menu-manager-container">
                <div id="menu-sortable" class="menu-list">
                    <?php $this->render_menu_items($hierarchical_pages); ?>
                </div>

                <div class="menu-actions">
                    <button type="button" id="save-menu-order" class="button button-primary">Lưu thứ tự</button>
                    <span id="save-status" class="save-status"></span>
                </div>
            </div>
        </div>

        <style>
        .menu-manager-container {
            max-width: 800px;
            margin-top: 20px;
        }

        .menu-list {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 0;
        }

        .menu-item {
            border-bottom: 1px solid #f0f0f1;
            padding: 15px;
            cursor: move;
            position: relative;
            background: #fff;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item.parent-item {
            background: #f8f9fa;
            font-weight: 600;
            color: #1d2327;
            cursor: default;
        }

        .menu-item.child-item {
            padding-left: 40px;
            background: #fff;
        }

        .menu-item.ui-sortable-helper {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 1px solid #007cba;
        }

        .menu-item.ui-sortable-placeholder {
            background: #e1f5fe;
            border: 2px dashed #007cba;
            height: 50px;
        }

        .menu-item-title {
            font-size: 14px;
            margin: 0;
        }

        .menu-item-info {
            font-size: 12px;
            color: #646970;
            margin-top: 5px;
        }

        .menu-item-actions {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }

        .menu-item-actions a {
            margin-left: 10px;
            text-decoration: none;
        }

        .menu-actions {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .save-status {
            margin-left: 10px;
            font-weight: 600;
        }

        .save-status.success {
            color: #00a32a;
        }

        .save-status.error {
            color: #d63638;
        }
        </style>
        <?php
    }

    private function render_menu_items($pages, $depth = 0) {
        foreach ($pages as $page) {
            $item_class = $page->is_parent ? 'parent-item' : 'child-item';
            $has_link_text = $page->has_link ? 'Có link' : 'Không có link';
            ?>
            <div class="menu-item <?php echo $item_class; ?>" data-id="<?php echo $page->id; ?>" data-parent-id="<?php echo $page->parent_id; ?>" data-depth="<?php echo $depth; ?>">
                <div class="menu-item-title">
                    <?php if ($page->icon): ?>
                        <i class="<?php echo esc_attr($page->icon); ?>"></i>
                    <?php endif; ?>
                    <?php echo esc_html($page->title); ?>
                </div>
                <div class="menu-item-info">
                    Slug: <?php echo esc_html($page->slug); ?> | <?php echo $has_link_text; ?>
                </div>
                <div class="menu-item-actions">
                    <?php if ($page->has_link): ?>
                        <a href="<?php echo admin_url('admin.php?page=huong-dan-' . $page->slug); ?>" class="button button-small">Chỉnh sửa</a>
                        <a href="<?php echo home_url('/huong-dan/' . $page->slug); ?>" class="button button-small" target="_blank">Xem</a>
                    <?php endif; ?>
                </div>
            </div>
            <?php
            if (!empty($page->children)) {
                $this->render_menu_items($page->children, $depth + 1);
            }
        }
    }

    public function ajax_update_menu_order() {
        check_ajax_referer('huong_dan_menu_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $menu_data = json_decode(stripslashes($_POST['menu_data']), true);

        if (!$menu_data) {
            wp_send_json_error('Invalid menu data');
        }

        HuongDan_Database::update_menu_order($menu_data);

        wp_send_json_success('Menu order updated successfully');
    }
    
    private function render_page_form($page, $is_new = false) {
        ?>
        <div class="wrap">
            <h1><?php echo $is_new ? 'Thêm trang mới' : 'Chỉnh sửa: ' . esc_html($page->title); ?></h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('huong_dan_save', 'huong_dan_nonce'); ?>
                <input type="hidden" name="page_id" value="<?php echo esc_attr($page->id); ?>">
                <input type="hidden" name="is_new" value="<?php echo $is_new ? '1' : '0'; ?>">
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Tiêu đề</th>
                        <td>
                            <input type="text" name="title" value="<?php echo esc_attr($page->title); ?>" class="regular-text" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Slug</th>
                        <td>
                            <input type="text" name="slug" value="<?php echo esc_attr($page->slug); ?>" class="regular-text" required>
                            <p class="description">URL thân thiện (chỉ chữ thường, số và dấu gạch ngang)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Loại mục</th>
                        <td>
                            <label>
                                <input type="radio" name="is_parent" value="1" <?php checked($page->is_parent, 1); ?> onchange="togglePageType()">
                                Mục cha (tiêu đề nhóm, không có link)
                            </label><br>
                            <label>
                                <input type="radio" name="is_parent" value="0" <?php checked($page->is_parent, 0); ?> onchange="togglePageType()">
                                Mục con (có link đến trang)
                            </label>
                        </td>
                    </tr>
                    <tr id="parent_select_row" style="<?php echo $page->is_parent ? 'display:none' : ''; ?>">
                        <th scope="row">Mục cha</th>
                        <td>
                            <select name="parent_id" class="regular-text">
                                <option value="0">-- Không có mục cha --</option>
                                <?php
                                $parent_pages = HuongDan_Database::get_all_pages();
                                foreach ($parent_pages as $parent_page) {
                                    if ($parent_page->is_parent && $parent_page->id != $page->id) {
                                        $selected = selected($page->parent_id, $parent_page->id, false);
                                        echo '<option value="' . $parent_page->id . '" ' . $selected . '>' . esc_html($parent_page->title) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Icon</th>
                        <td>
                            <input type="text" name="icon" value="<?php echo esc_attr($page->icon); ?>" class="regular-text" placeholder="fas fa-home">
                            <p class="description">Font Awesome icon class (ví dụ: fas fa-home, fab fa-facebook)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Thứ tự menu</th>
                        <td>
                            <input type="number" name="menu_order" value="<?php echo esc_attr($page->menu_order); ?>" class="small-text">
                        </td>
                    </tr>
                    <tr id="content_row" style="<?php echo $page->is_parent ? 'display:none' : ''; ?>">
                        <th scope="row">Nội dung</th>
                        <td>
                            <?php
                            wp_editor($page->content, 'page_content', array(
                                'textarea_name' => 'content',
                                'media_buttons' => true,
                                'textarea_rows' => 20,
                                'teeny' => false,
                                'tinymce' => true,
                                'quicktags' => true
                            ));
                            ?>
                        </td>
                    </tr>
                </table>

                <script>
                function togglePageType() {
                    var isParent = document.querySelector('input[name="is_parent"]:checked').value == '1';
                    var parentRow = document.getElementById('parent_select_row');
                    var contentRow = document.getElementById('content_row');

                    if (isParent) {
                        parentRow.style.display = 'none';
                        contentRow.style.display = 'none';
                        document.querySelector('select[name="parent_id"]').value = '0';
                    } else {
                        parentRow.style.display = 'table-row';
                        contentRow.style.display = 'table-row';
                    }
                }
                </script>
                
                <p class="submit">
                    <input type="submit" name="huong_dan_save" class="button-primary" value="<?php echo $is_new ? 'Tạo trang' : 'Cập nhật'; ?>">
                    <?php if (!$is_new): ?>
                    <input type="submit" name="huong_dan_delete" class="button" value="Xóa trang" onclick="return confirm('Bạn có chắc chắn muốn xóa trang này?')">
                    <?php endif; ?>
                </p>
            </form>
        </div>
        <?php
    }
    
    private function save_page() {
        if (!wp_verify_nonce($_POST['huong_dan_nonce'], 'huong_dan_save')) {
            wp_die('Nonce verification failed');
        }

        $page_id = intval($_POST['page_id']);
        $is_new = $_POST['is_new'] === '1';
        $title = sanitize_text_field($_POST['title']);
        $slug = sanitize_title($_POST['slug']);
        $content = wp_kses_post($_POST['content']);
        $menu_order = intval($_POST['menu_order']);
        $parent_id = intval($_POST['parent_id']);
        $is_parent = intval($_POST['is_parent']);
        $icon = sanitize_text_field($_POST['icon']);
        $has_link = $is_parent ? 0 : 1; // Parent items don't have links

        if ($is_new) {
            HuongDan_Database::create_page($slug, $title, $content, $menu_order, $parent_id, $is_parent, $has_link, $icon);
            if ($has_link) {
                wp_redirect(admin_url('admin.php?page=huong-dan-' . $slug . '&message=created'));
            } else {
                wp_redirect(admin_url('admin.php?page=huong-dan&message=created'));
            }
        } else {
            HuongDan_Database::update_page($page_id, array(
                'title' => $title,
                'slug' => $slug,
                'content' => $content,
                'parent_id' => $parent_id,
                'menu_order' => $menu_order,
                'is_parent' => $is_parent,
                'has_link' => $has_link,
                'icon' => $icon,
                'depth' => $parent_id > 0 ? 1 : 0
            ));
            if ($has_link) {
                wp_redirect(admin_url('admin.php?page=huong-dan-' . $slug . '&message=updated'));
            } else {
                wp_redirect(admin_url('admin.php?page=huong-dan&message=updated'));
            }
        }
        exit;
    }
    
    private function delete_page() {
        if (!wp_verify_nonce($_POST['huong_dan_nonce'], 'huong_dan_save')) {
            wp_die('Nonce verification failed');
        }
        
        $page_id = intval($_POST['page_id']);
        HuongDan_Database::delete_page($page_id);
        wp_redirect(admin_url('admin.php?page=huong-dan&message=deleted'));
        exit;
    }
}
