<?php
/**
 * Database handler for <PERSON><PERSON> plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Database {
    
    private static $table_name = 'huong_dan_pages';
    
    public static function create_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            slug varchar(100) NOT NULL,
            title varchar(255) NOT NULL,
            content longtext,
            parent_id mediumint(9) DEFAULT 0,
            menu_order int(11) DEFAULT 0,
            depth int(11) DEFAULT 0,
            is_parent tinyint(1) DEFAULT 0,
            has_link tinyint(1) DEFAULT 1,
            icon varchar(100) DEFAULT '',
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug),
            KEY parent_id (parent_id),
            KEY menu_order (menu_order)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        // Debug: Log table creation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Huong Dan Plugin: Table creation result: ' . print_r($result, true));
        }

        // Verify table was created
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            error_log('Huong Dan Plugin: Failed to create table ' . $table_name);
        }

        return $table_exists;
    }
    
    public static function create_page($slug, $title, $content = '', $menu_order = 0, $parent_id = 0, $is_parent = 0, $has_link = 1, $icon = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        // Check if table exists first
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            error_log('Huong Dan Plugin: Table does not exist when trying to create page: ' . $slug);
            return false;
        }

        // Check if page already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE slug = %s",
            $slug
        ));

        if (!$existing) {
            $result = $wpdb->insert(
                $table_name,
                array(
                    'slug' => $slug,
                    'title' => $title,
                    'content' => $content,
                    'parent_id' => $parent_id,
                    'menu_order' => $menu_order,
                    'depth' => $parent_id > 0 ? 1 : 0,
                    'is_parent' => $is_parent,
                    'has_link' => $has_link,
                    'icon' => $icon
                ),
                array('%s', '%s', '%s', '%d', '%d', '%d', '%d', '%d', '%s')
            );

            if ($result === false) {
                error_log('Huong Dan Plugin: Failed to insert page: ' . $slug . ' Error: ' . $wpdb->last_error);
                return false;
            }

            return $wpdb->insert_id;
        }

        return $existing;
    }
    
    public static function get_page($slug) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE slug = %s AND status = 'active'",
            $slug
        ));
    }

    public static function get_page_by_id($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d AND status = 'active'",
            $id
        ));
    }
    
    public static function get_all_pages() {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        return $wpdb->get_results(
            "SELECT * FROM $table_name WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC, title ASC"
        );
    }

    public static function get_hierarchical_pages() {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        $pages = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE status = 'active' ORDER BY parent_id ASC, menu_order ASC"
        );

        return self::build_hierarchy($pages);
    }

    private static function build_hierarchy($pages, $parent_id = 0) {
        $hierarchy = array();

        foreach ($pages as $page) {
            if ($page->parent_id == $parent_id) {
                $page->children = self::build_hierarchy($pages, $page->id);
                $hierarchy[] = $page;
            }
        }

        return $hierarchy;
    }

    public static function update_menu_order($menu_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        foreach ($menu_data as $item) {
            $wpdb->update(
                $table_name,
                array(
                    'parent_id' => $item['parent_id'],
                    'menu_order' => $item['menu_order'],
                    'depth' => $item['depth']
                ),
                array('id' => $item['id']),
                array('%d', '%d', '%d'),
                array('%d')
            );
        }
    }
    
    public static function update_page($id, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->update(
            $table_name,
            $data,
            array('id' => $id),
            array('%s', '%s', '%s'),
            array('%d')
        );
    }
    
    public static function delete_page($id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->update(
            $table_name,
            array('status' => 'deleted'),
            array('id' => $id),
            array('%s'),
            array('%d')
        );
    }
}
