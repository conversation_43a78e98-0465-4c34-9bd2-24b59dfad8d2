<?php
/**
 * Database handler for Huong Dan plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Database {
    
    private static $table_name = 'huong_dan_pages';
    
    public static function create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            slug varchar(100) NOT NULL,
            title varchar(255) NOT NULL,
            content longtext,
            menu_order int(11) DEFAULT 0,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY slug (slug)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    public static function create_page($slug, $title, $content = '', $menu_order = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        // Check if page already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE slug = %s",
            $slug
        ));
        
        if (!$existing) {
            $wpdb->insert(
                $table_name,
                array(
                    'slug' => $slug,
                    'title' => $title,
                    'content' => $content,
                    'menu_order' => $menu_order
                ),
                array('%s', '%s', '%s', '%d')
            );
        }
    }
    
    public static function get_page($slug) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE slug = %s AND status = 'active'",
            $slug
        ));
    }
    
    public static function get_all_pages() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->get_results(
            "SELECT * FROM $table_name WHERE status = 'active' ORDER BY menu_order ASC, title ASC"
        );
    }
    
    public static function update_page($id, $data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->update(
            $table_name,
            $data,
            array('id' => $id),
            array('%s', '%s', '%s'),
            array('%d')
        );
    }
    
    public static function delete_page($id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        return $wpdb->update(
            $table_name,
            array('status' => 'deleted'),
            array('id' => $id),
            array('%s'),
            array('%d')
        );
    }
}
