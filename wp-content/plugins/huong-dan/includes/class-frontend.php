<?php
/**
 * Frontend functionality for Huong Dan plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Frontend {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'add_critical_css'));
        add_action('wp_footer', array($this, 'add_debug_script'));
    }


    
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^huong-dan/([^/]+)/?$',
            'index.php?huong_dan_page=$matches[1]',
            'top'
        );

        add_rewrite_rule(
            '^huong-dan/?$',
            'index.php?huong_dan_page=sepay-la-gi',
            'top'
        );
    }

    public function maybe_flush_rewrite_rules() {
        if (get_option('huong_dan_rewrite_rules_flushed') !== '1') {
            flush_rewrite_rules();
            update_option('huong_dan_rewrite_rules_flushed', '1');
        }
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'huong_dan_page';
        return $vars;
    }
    
    public function template_redirect() {
        $page_slug = get_query_var('huong_dan_page');
        
        if ($page_slug) {
            $this->display_page($page_slug);
            exit;
        }
    }
    
    public function enqueue_scripts() {
        if (get_query_var('huong_dan_page')) {
            // Enqueue Bootstrap CSS
            wp_enqueue_style(
                'bootstrap-css',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
                array(),
                '5.1.3'
            );
            
            // Enqueue Bootstrap JS
            wp_enqueue_script(
                'bootstrap-js',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
                array('jquery'),
                '5.1.3',
                true
            );
            
            // Enqueue custom CSS with high priority
            wp_enqueue_style(
                'huong-dan-css',
                HUONG_DAN_PLUGIN_URL . 'assets/style.css',
                array('bootstrap-css'),
                HUONG_DAN_VERSION . '-' . time()
            );
            
            // Enqueue custom JS
            wp_enqueue_script(
                'huong-dan-js',
                HUONG_DAN_PLUGIN_URL . 'assets/script.js',
                array('jquery', 'bootstrap-js'),
                HUONG_DAN_VERSION,
                true
            );
        }
    }
    
    public function add_critical_css() {
        if (get_query_var('huong_dan_page')) {
            echo '<style type="text/css">
                .sidebar { 
                    display: block !important; 
                    visibility: visible !important; 
                    opacity: 1 !important; 
                }
                .sidebar-header { display: block !important; visibility: visible !important; }
                .sidebar-menu { display: block !important; visibility: visible !important; }
                .sidebar-menu li { display: block !important; visibility: visible !important; }
                .sidebar-menu a { display: block !important; visibility: visible !important; }
                
                /* Force sidebar to show on desktop */
                @media (min-width: 769px) {
                    .sidebar {
                        left: 0 !important;
                        position: fixed !important;
                        width: 250px !important;
                        height: 100vh !important;
                        top: 60px !important;
                        z-index: 100 !important;
                    }
                }
                
                /* Mobile sidebar control */
                @media (max-width: 768px) {
                    .sidebar {
                        left: -250px !important;
                        transition: left 0.3s ease !important;
                    }
                    .sidebar.show {
                        left: 0 !important;
                    }
                    .content-area {
                        margin-left: 0 !important;
                    }
                }
            </style>';
        }
    }
    
    public function add_debug_script() {
        if (get_query_var('huong_dan_page')) {
            echo '<script>
                console.log("Huong Dan Debug: Checking sidebar...");
                const sidebar = document.querySelector(".sidebar");
                const sidebarMenus = document.querySelectorAll(".sidebar-menu");
                const sidebarLinks = document.querySelectorAll(".sidebar-menu a");
                
                console.log("Sidebar element:", sidebar);
                console.log("Sidebar menus found:", sidebarMenus.length);
                console.log("Sidebar links found:", sidebarLinks.length);
                
                if (sidebar) {
                    const styles = window.getComputedStyle(sidebar);
                    console.log("Sidebar display:", styles.display);
                    console.log("Sidebar visibility:", styles.visibility);
                    console.log("Sidebar left:", styles.left);
                    console.log("Sidebar width:", styles.width);
                }
            </script>';
        }
    }
    
    private function display_page($slug) {
        $page = HuongDan_Database::get_page($slug);
        $all_pages = HuongDan_Database::get_all_pages();

        if (!$page) {
            // Try to find the first available page as fallback
            $first_page = HuongDan_Database::get_first_available_page();
            if ($first_page) {
                wp_redirect('/huong-dan/' . $first_page->slug);
                exit;
            }
            wp_die('Trang không tồn tại', 'Lỗi 404', array('response' => 404));
        }

        // Set page title
        add_filter('wp_title', function($title) use ($page) {
            return $page->title . ' - Hướng dẫn SePay';
        });

        // Set document title
        add_filter('document_title_parts', function($title) use ($page) {
            $title['title'] = $page->title;
            $title['site'] = 'Hướng dẫn SePay';
            return $title;
        });

        // Include template
        include HUONG_DAN_PLUGIN_PATH . 'templates/page-template.php';
    }
    
    public static function get_menu_items() {
        return HuongDan_Database::get_hierarchical_pages();
    }

    public static function render_sidebar_menu($current_slug = '') {
        $menu_items = self::get_menu_items();

        foreach ($menu_items as $parent_item) {
            if ($parent_item->is_parent && !$parent_item->has_link) {
                // Render parent category header
                echo '<div class="sidebar mt-3">';
                echo '<div class="sidebar-header">';
                if ($parent_item->icon) {
                    echo '<i class="' . esc_attr($parent_item->icon) . '"></i> ';
                }
                echo esc_html($parent_item->title);
                echo '</div>';

                if (!empty($parent_item->children)) {
                    echo '<ul class="sidebar-menu">';
                    foreach ($parent_item->children as $child_item) {
                        if ($child_item->has_link) {
                            $is_active = ($child_item->slug === $current_slug) ? 'active' : '';
                            echo '<li>';
                            echo '<a href="/huong-dan/' . esc_attr($child_item->slug) . '" class="' . $is_active . '">';
                            if ($child_item->icon) {
                                echo '<i class="' . esc_attr($child_item->icon) . '"></i>';
                            } else {
                                echo '<i class="fas fa-file-alt"></i>';
                            }
                            echo esc_html($child_item->title);
                            echo '</a>';
                            echo '</li>';
                        }
                    }
                    echo '</ul>';
                }
                echo '</div>';
            } else if ($parent_item->has_link && $parent_item->parent_id == 0) {
                // Render standalone page (no parent)
                echo '<div class="sidebar mt-3">';
                echo '<ul class="sidebar-menu">';
                $is_active = ($parent_item->slug === $current_slug) ? 'active' : '';
                echo '<li>';
                echo '<a href="/huong-dan/' . esc_attr($parent_item->slug) . '" class="' . $is_active . '">';
                if ($parent_item->icon) {
                    echo '<i class="' . esc_attr($parent_item->icon) . '"></i>';
                } else {
                    echo '<i class="fas fa-file-alt"></i>';
                }
                echo esc_html($parent_item->title);
                echo '</a>';
                echo '</li>';
                echo '</ul>';
                echo '</div>';
            }
        }
    }
}
