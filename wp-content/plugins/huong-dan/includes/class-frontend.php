<?php
/**
 * Frontend functionality for <PERSON>ong <PERSON> plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Frontend {
    
    public function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        add_action('template_redirect', array($this, 'template_redirect'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^huong-dan/([^/]+)/?$',
            'index.php?huong_dan_page=$matches[1]',
            'top'
        );
        
        add_rewrite_rule(
            '^huong-dan/?$',
            'index.php?huong_dan_page=gioi-thieu',
            'top'
        );
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'huong_dan_page';
        return $vars;
    }
    
    public function template_redirect() {
        $page_slug = get_query_var('huong_dan_page');
        
        if ($page_slug) {
            $this->display_page($page_slug);
            exit;
        }
    }
    
    public function enqueue_scripts() {
        if (get_query_var('huong_dan_page')) {
            // Enqueue Bootstrap CSS
            wp_enqueue_style(
                'bootstrap-css',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
                array(),
                '5.1.3'
            );
            
            // Enqueue Bootstrap JS
            wp_enqueue_script(
                'bootstrap-js',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
                array('jquery'),
                '5.1.3',
                true
            );
            
            // Enqueue custom CSS
            wp_enqueue_style(
                'huong-dan-css',
                HUONG_DAN_PLUGIN_URL . 'assets/style.css',
                array('bootstrap-css'),
                HUONG_DAN_VERSION
            );
            
            // Enqueue custom JS
            wp_enqueue_script(
                'huong-dan-js',
                HUONG_DAN_PLUGIN_URL . 'assets/script.js',
                array('jquery', 'bootstrap-js'),
                HUONG_DAN_VERSION,
                true
            );
        }
    }
    
    private function display_page($slug) {
        $page = HuongDan_Database::get_page($slug);
        $all_pages = HuongDan_Database::get_all_pages();
        
        if (!$page) {
            wp_die('Trang không tồn tại', 'Lỗi 404', array('response' => 404));
        }
        
        // Set page title
        add_filter('wp_title', function($title) use ($page) {
            return $page->title . ' - Hướng dẫn SePay';
        });
        
        // Include template
        include HUONG_DAN_PLUGIN_PATH . 'templates/page-template.php';
    }
    
    public static function get_menu_items() {
        return array(
            'gioi-thieu' => array(
                'title' => 'SePay là gì?',
                'icon' => 'fas fa-info-circle'
            ),
            'dang-ky-sepay' => array(
                'title' => 'Đăng ký SePay',
                'icon' => 'fas fa-user-plus'
            ),
            'them-tai-khoan-ngan-hang' => array(
                'title' => 'Thêm tài khoản ngân hàng',
                'icon' => 'fas fa-university'
            ),
            'xem-giao-dich' => array(
                'title' => 'Xem giao dịch',
                'icon' => 'fas fa-list'
            ),
            'nguoi-dung-phan-quyen' => array(
                'title' => 'Người dùng & Phân quyền',
                'icon' => 'fas fa-users'
            ),
            'tai-khoan-phu' => array(
                'title' => 'Tài khoản phụ',
                'icon' => 'fas fa-user-friends'
            ),
            'cau-hinh-tk-ngan-hang' => array(
                'title' => 'Cấu hình TK ngân hàng',
                'icon' => 'fas fa-cog'
            ),
            'goi-dich-vu' => array(
                'title' => 'Gói dịch vụ',
                'icon' => 'fas fa-box'
            ),
            'hoa-don-thanh-toan' => array(
                'title' => 'Hóa đơn & Thanh toán',
                'icon' => 'fas fa-receipt'
            ),
            'cau-hinh-cong-ty' => array(
                'title' => 'Cấu hình công ty',
                'icon' => 'fas fa-building'
            ),
            'tich-hop-telegram' => array(
                'title' => 'Tích hợp Telegram',
                'icon' => 'fab fa-telegram'
            ),
            'tich-hop-lark-messenger' => array(
                'title' => 'Tích hợp Lark Messenger',
                'icon' => 'fas fa-comment'
            ),
            'tich-hop-viber' => array(
                'title' => 'Tích hợp Viber',
                'icon' => 'fab fa-viber'
            ),
            'mobile-app' => array(
                'title' => 'Mobile App',
                'icon' => 'fas fa-mobile-alt'
            )
        );
    }
}
