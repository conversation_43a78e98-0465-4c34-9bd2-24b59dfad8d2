<?php
/**
 * Frontend functionality for <PERSON>ong <PERSON> plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class HuongDan_Frontend {
    
    public function __construct() {
        add_action('init', array($this, 'add_rewrite_rules'));
        add_filter('query_vars', array($this, 'add_query_vars'));
        add_action('template_redirect', array($this, 'template_redirect'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^huong-dan/([^/]+)/?$',
            'index.php?huong_dan_page=$matches[1]',
            'top'
        );
        
        add_rewrite_rule(
            '^huong-dan/?$',
            'index.php?huong_dan_page=gioi-thieu',
            'top'
        );
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'huong_dan_page';
        return $vars;
    }
    
    public function template_redirect() {
        $page_slug = get_query_var('huong_dan_page');
        
        if ($page_slug) {
            $this->display_page($page_slug);
            exit;
        }
    }
    
    public function enqueue_scripts() {
        if (get_query_var('huong_dan_page')) {
            // Enqueue Bootstrap CSS
            wp_enqueue_style(
                'bootstrap-css',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
                array(),
                '5.1.3'
            );
            
            // Enqueue Bootstrap JS
            wp_enqueue_script(
                'bootstrap-js',
                'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
                array('jquery'),
                '5.1.3',
                true
            );
            
            // Enqueue custom CSS
            wp_enqueue_style(
                'huong-dan-css',
                HUONG_DAN_PLUGIN_URL . 'assets/style.css',
                array('bootstrap-css'),
                HUONG_DAN_VERSION
            );
            
            // Enqueue custom JS
            wp_enqueue_script(
                'huong-dan-js',
                HUONG_DAN_PLUGIN_URL . 'assets/script.js',
                array('jquery', 'bootstrap-js'),
                HUONG_DAN_VERSION,
                true
            );
        }
    }
    
    private function display_page($slug) {
        $page = HuongDan_Database::get_page($slug);
        $all_pages = HuongDan_Database::get_all_pages();
        
        if (!$page) {
            wp_die('Trang không tồn tại', 'Lỗi 404', array('response' => 404));
        }
        
        // Set page title
        add_filter('wp_title', function($title) use ($page) {
            return $page->title . ' - Hướng dẫn SePay';
        });
        
        // Include template
        include HUONG_DAN_PLUGIN_PATH . 'templates/page-template.php';
    }
    
    public static function get_menu_items() {
        return HuongDan_Database::get_hierarchical_pages();
    }

    public static function render_sidebar_menu($current_slug = '') {
        $menu_items = self::get_menu_items();

        foreach ($menu_items as $parent_item) {
            if ($parent_item->is_parent && !$parent_item->has_link) {
                // Render parent category header
                echo '<div class="sidebar mt-3">';
                echo '<div class="sidebar-header">';
                if ($parent_item->icon) {
                    echo '<i class="' . esc_attr($parent_item->icon) . '"></i> ';
                }
                echo esc_html($parent_item->title);
                echo '</div>';

                if (!empty($parent_item->children)) {
                    echo '<ul class="sidebar-menu">';
                    foreach ($parent_item->children as $child_item) {
                        if ($child_item->has_link) {
                            $is_active = ($child_item->slug === $current_slug) ? 'active' : '';
                            echo '<li>';
                            echo '<a href="' . home_url('/huong-dan/' . $child_item->slug) . '" class="' . $is_active . '">';
                            if ($child_item->icon) {
                                echo '<i class="' . esc_attr($child_item->icon) . '"></i>';
                            } else {
                                echo '<i class="fas fa-file-alt"></i>';
                            }
                            echo esc_html($child_item->title);
                            echo '</a>';
                            echo '</li>';
                        }
                    }
                    echo '</ul>';
                }
                echo '</div>';
            } else if ($parent_item->has_link && $parent_item->parent_id == 0) {
                // Render standalone page (no parent)
                echo '<div class="sidebar mt-3">';
                echo '<ul class="sidebar-menu">';
                $is_active = ($parent_item->slug === $current_slug) ? 'active' : '';
                echo '<li>';
                echo '<a href="' . home_url('/huong-dan/' . $parent_item->slug) . '" class="' . $is_active . '">';
                if ($parent_item->icon) {
                    echo '<i class="' . esc_attr($parent_item->icon) . '"></i>';
                } else {
                    echo '<i class="fas fa-file-alt"></i>';
                }
                echo esc_html($parent_item->title);
                echo '</a>';
                echo '</li>';
                echo '</ul>';
                echo '</div>';
            }
        }
    }
}
