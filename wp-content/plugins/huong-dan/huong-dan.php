<?php
/**
 * Plugin Name: Hướng dẫn SePay
 * Plugin URI: https://example.com
 * Description: Plugin tạo trang hướng dẫn với menu và editor WordPress, sử dụng Bootstrap
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HUONG_DAN_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HUONG_DAN_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('HUONG_DAN_VERSION', '1.0.0');

// Include required files
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-admin.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-frontend.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-database.php';

// Initialize the plugin
class HuongDanPlugin {

    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    public function init() {
        new HuongDan_Admin();
        new HuongDan_Frontend();
        new HuongDan_Database();
    }

    public function activate() {
        // Create database tables
        HuongDan_Database::create_tables();

        // Create default pages
        $this->create_default_pages();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    private function create_default_pages() {
        $default_pages = array(
            'gioi-thieu' => 'Giới thiệu SePay',
            'dang-ky-sepay' => 'Đăng ký SePay',
            'them-tai-khoan-ngan-hang' => 'Thêm tài khoản ngân hàng',
            'xem-giao-dich' => 'Xem giao dịch',
            'nguoi-dung-phan-quyen' => 'Người dùng & Phân quyền',
            'tai-khoan-phu' => 'Tài khoản phụ',
            'cau-hinh-tk-ngan-hang' => 'Cấu hình TK ngân hàng',
            'goi-dich-vu' => 'Gói dịch vụ',
            'hoa-don-thanh-toan' => 'Hóa đơn & Thanh toán',
            'cau-hinh-cong-ty' => 'Cấu hình công ty',
            'tich-hop-telegram' => 'Tích hợp Telegram',
            'tich-hop-lark-messenger' => 'Tích hợp Lark Messenger',
            'tich-hop-viber' => 'Tích hợp Viber',
            'mobile-app' => 'Mobile App'
        );

        foreach ($default_pages as $slug => $title) {
            HuongDan_Database::create_page($slug, $title, '');
        }
    }
}

// Initialize the plugin
new HuongDanPlugin();