<?php
/**
 * Plugin Name: Hướng dẫn SePay
 * Plugin URI: https://example.com
 * Description: Plugin tạo trang hướng dẫn với menu và editor WordPress, sử dụng Bootstrap
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HUONG_DAN_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HUONG_DAN_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('HUONG_DAN_VERSION', '1.0.0');

// Include required files
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-admin.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-frontend.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-database.php';

// Initialize the plugin
class HuongDanPlugin {

    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    public function init() {
        new HuongDan_Admin();
        new HuongDan_Frontend();
        new HuongDan_Database();
    }

    public function activate() {
        // Create database tables
        HuongDan_Database::create_tables();

        // Create default pages
        $this->create_default_pages();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    private function create_default_pages() {
        // Create parent categories first
        $parent_categories = array(
            array(
                'slug' => 'gioi-thieu',
                'title' => 'GIỚI THIỆU',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 1,
                'icon' => 'fas fa-info-circle'
            ),
            array(
                'slug' => 'goi-dich-vu',
                'title' => 'GÓI DỊCH VỤ',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 2,
                'icon' => 'fas fa-box'
            ),
            array(
                'slug' => 'huong-dan-chung',
                'title' => 'HƯỚNG DẪN CHUNG',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 3,
                'icon' => 'fas fa-book'
            ),
            array(
                'slug' => 'cau-hinh-cong-ty',
                'title' => 'CẤU HÌNH CÔNG TY',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 4,
                'icon' => 'fas fa-building'
            ),
            array(
                'slug' => 'chia-se-bien-dong-so-du',
                'title' => 'CHIA SẺ BIẾN ĐỘNG SỐ DƯ',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 5,
                'icon' => 'fas fa-share-alt'
            )
        );

        foreach ($parent_categories as $category) {
            HuongDan_Database::create_page(
                $category['slug'],
                $category['title'],
                '',
                $category['menu_order'],
                0,
                $category['is_parent'],
                $category['has_link'],
                $category['icon']
            );
        }

        // Get parent IDs
        $gioi_thieu_id = $this->get_page_id_by_slug('gioi-thieu');
        $goi_dich_vu_id = $this->get_page_id_by_slug('goi-dich-vu');
        $huong_dan_chung_id = $this->get_page_id_by_slug('huong-dan-chung');
        $cau_hinh_cong_ty_id = $this->get_page_id_by_slug('cau-hinh-cong-ty');
        $chia_se_bien_dong_id = $this->get_page_id_by_slug('chia-se-bien-dong-so-du');

        // Create child pages
        $child_pages = array(
            // Giới thiệu children
            array(
                'slug' => 'sepay-la-gi',
                'title' => 'SePay là gì?',
                'parent_id' => $gioi_thieu_id,
                'menu_order' => 1,
                'icon' => ''
            ),

            // Gói dịch vụ children
            array(
                'slug' => 'goi-theo-diem-ban',
                'title' => 'Gói theo điểm bán',
                'parent_id' => $goi_dich_vu_id,
                'menu_order' => 1,
                'icon' => ''
            ),

            // Hướng dẫn chung children
            array(
                'slug' => 'dang-ky-sepay',
                'title' => 'Đăng ký SePay',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 1,
                'icon' => ''
            ),
            array(
                'slug' => 'them-tai-khoan-ngan-hang',
                'title' => 'Thêm tài khoản ngân hàng',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 2,
                'icon' => ''
            ),
            array(
                'slug' => 'xem-giao-dich',
                'title' => 'Xem giao dịch',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 3,
                'icon' => ''
            ),
            array(
                'slug' => 'nguoi-dung-phan-quyen',
                'title' => 'Người dùng & Phân quyền',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 4,
                'icon' => ''
            ),
            array(
                'slug' => 'tai-khoan-phu',
                'title' => 'Tài khoản phụ',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 5,
                'icon' => ''
            ),
            array(
                'slug' => 'cau-hinh-tk-ngan-hang',
                'title' => 'Cấu hình TK ngân hàng',
                'parent_id' => $huong_dan_chung_id,
                'menu_order' => 6,
                'icon' => ''
            ),

            // Cấu hình công ty children
            array(
                'slug' => 'goi-dich-vu-cty',
                'title' => 'Gói dịch vụ',
                'parent_id' => $cau_hinh_cong_ty_id,
                'menu_order' => 1,
                'icon' => ''
            ),
            array(
                'slug' => 'hoa-don-thanh-toan',
                'title' => 'Hóa đơn & thanh toán',
                'parent_id' => $cau_hinh_cong_ty_id,
                'menu_order' => 2,
                'icon' => ''
            ),
            array(
                'slug' => 'cau-hinh-chung',
                'title' => 'Cấu hình chung',
                'parent_id' => $cau_hinh_cong_ty_id,
                'menu_order' => 3,
                'icon' => ''
            ),

            // Chia sẻ biến động số dư children
            array(
                'slug' => 'tich-hop-telegram',
                'title' => 'Tích hợp Telegram ✓',
                'parent_id' => $chia_se_bien_dong_id,
                'menu_order' => 1,
                'icon' => 'fab fa-telegram'
            ),
            array(
                'slug' => 'tich-hop-lark-messenger',
                'title' => 'Tích hợp Lark Messenger ✓',
                'parent_id' => $chia_se_bien_dong_id,
                'menu_order' => 2,
                'icon' => 'fas fa-comment'
            ),
            array(
                'slug' => 'tich-hop-viber',
                'title' => 'Tích hợp Viber ✓',
                'parent_id' => $chia_se_bien_dong_id,
                'menu_order' => 3,
                'icon' => 'fab fa-viber'
            ),
            array(
                'slug' => 'mobile-app',
                'title' => 'Mobile App ✓',
                'parent_id' => $chia_se_bien_dong_id,
                'menu_order' => 4,
                'icon' => 'fas fa-mobile-alt'
            )
        );

        foreach ($child_pages as $page) {
            HuongDan_Database::create_page(
                $page['slug'],
                $page['title'],
                '',
                $page['menu_order'],
                $page['parent_id'],
                0,
                1,
                $page['icon']
            );
        }
    }

    private function get_page_id_by_slug($slug) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'huong_dan_pages';
        return $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE slug = %s",
            $slug
        ));
    }
}

// Initialize the plugin
new HuongDanPlugin();