<?php
/**
 * Plugin Name: Hướng dẫn SePay
 * Plugin URI: https://example.com
 * Description: Plugin tạo trang hướng dẫn với menu và editor WordPress, sử dụng Bootstrap
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HUONG_DAN_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HUONG_DAN_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('HUONG_DAN_VERSION', '1.6.0');

// Include required files
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-admin.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-frontend.php';
require_once HUONG_DAN_PLUGIN_PATH . 'includes/class-database.php';

// Initialize the plugin
class HuongDanPlugin {

    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    public function init() {
        // Check if tables exist, if not create them
        if (!get_option('huong_dan_activated')) {
            $this->activate();
        }

        // Add rewrite rules on every init
        add_action('init', array($this, 'add_rewrite_rules'), 10, 0);

        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));

        // Handle template redirect
        add_action('template_redirect', array($this, 'template_redirect'));

        new HuongDan_Admin();
        new HuongDan_Frontend();
        new HuongDan_Database();

        // Add shortcode for testing
        add_shortcode('huong_dan_test', array($this, 'shortcode_test'));
    }

    public function add_query_vars($vars) {
        $vars[] = 'huong_dan_page';
        return $vars;
    }

    public function template_redirect() {
        $page_slug = get_query_var('huong_dan_page');

        if ($page_slug) {
            $this->display_page($page_slug);
            exit;
        }
    }

    private function display_page($slug) {
        $page = HuongDan_Database::get_page($slug);

        if (!$page) {
            // Try to find the first available page as fallback
            $first_page = HuongDan_Database::get_first_available_page();
            if ($first_page) {
                wp_redirect(home_url('/huong-dan/' . $first_page->slug));
                exit;
            }
            wp_die('Trang không tồn tại', 'Lỗi 404', array('response' => 404));
        }

        // Set page title
        add_filter('wp_title', function($title) use ($page) {
            return $page->title . ' - Hướng dẫn SePay';
        });

        // Set document title
        add_filter('document_title_parts', function($title) use ($page) {
            $title['title'] = $page->title;
            $title['site'] = 'Hướng dẫn SePay';
            return $title;
        });

        // Include template
        include HUONG_DAN_PLUGIN_PATH . 'templates/page-template.php';
    }

    public function shortcode_test($atts) {
        $atts = shortcode_atts(array(
            'slug' => 'sepay-la-gi'
        ), $atts);

        $page = HuongDan_Database::get_page($atts['slug']);

        if (!$page) {
            return '<p>Trang không tồn tại: ' . esc_html($atts['slug']) . '</p>';
        }

        ob_start();
        include HUONG_DAN_PLUGIN_PATH . 'templates/page-template.php';
        return ob_get_clean();
    }

    public function activate() {
        // Create database tables
        HuongDan_Database::create_tables();

        // Wait a moment for table creation
        sleep(1);

        // Create default pages
        $this->create_default_pages();

        // Add rewrite rules
        $this->add_rewrite_rules();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Set activation flag
        update_option('huong_dan_activated', true);
        update_option('huong_dan_rewrite_rules_flushed', '0'); // Force flush on next load
    }

    public function add_rewrite_rules() {
        add_rewrite_rule(
            '^huong-dan/([^/]+)/?$',
            'index.php?huong_dan_page=$matches[1]',
            'top'
        );

        add_rewrite_rule(
            '^huong-dan/?$',
            'index.php?huong_dan_page=sepay-la-gi',
            'top'
        );
    }

    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    public function create_default_pages() {
        // Create parent categories first
        $parent_categories = array(
            array(
                'slug' => 'gioi-thieu',
                'title' => 'GIỚI THIỆU',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 1,
                'icon' => 'fas fa-info-circle'
            ),
            array(
                'slug' => 'goi-dich-vu',
                'title' => 'GÓI DỊCH VỤ',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 2,
                'icon' => 'fas fa-box'
            ),
            array(
                'slug' => 'huong-dan-chung',
                'title' => 'HƯỚNG DẪN CHUNG',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 3,
                'icon' => 'fas fa-book'
            ),
            array(
                'slug' => 'cau-hinh-cong-ty',
                'title' => 'CẤU HÌNH CÔNG TY',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 4,
                'icon' => 'fas fa-building'
            ),
            array(
                'slug' => 'chia-se-bien-dong-so-du',
                'title' => 'CHIA SẺ BIẾN ĐỘNG SỐ DƯ',
                'is_parent' => 1,
                'has_link' => 0,
                'menu_order' => 5,
                'icon' => 'fas fa-share-alt'
            )
        );

        $parent_ids = array();

        foreach ($parent_categories as $category) {
            $id = HuongDan_Database::create_page(
                $category['slug'],
                $category['title'],
                '',
                $category['menu_order'],
                0,
                $category['is_parent'],
                $category['has_link'],
                $category['icon']
            );
            $parent_ids[$category['slug']] = $id;
        }

        // Create child pages
        $child_pages = array(
            // Giới thiệu children
            array(
                'slug' => 'sepay-la-gi',
                'title' => 'SePay là gì?',
                'parent_slug' => 'gioi-thieu',
                'menu_order' => 1,
                'icon' => '',
                'content' => '<p>SePay là một nền tảng thanh toán điện tử hiện đại, giúp doanh nghiệp quản lý giao dịch một cách hiệu quả.</p>'
            ),

            // Gói dịch vụ children
            array(
                'slug' => 'goi-theo-diem-ban',
                'title' => 'Gói theo điểm bán',
                'parent_slug' => 'goi-dich-vu',
                'menu_order' => 1,
                'icon' => '',
                'content' => '<p>Hướng dẫn về các gói dịch vụ theo điểm bán của SePay.</p>'
            ),

            // Hướng dẫn chung children
            array(
                'slug' => 'dang-ky-sepay',
                'title' => 'Đăng ký SePay',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 1,
                'icon' => '',
                'content' => '<p>Hướng dẫn chi tiết cách đăng ký tài khoản SePay.</p>'
            ),
            array(
                'slug' => 'them-tai-khoan-ngan-hang',
                'title' => 'Thêm tài khoản ngân hàng',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 2,
                'icon' => '',
                'content' => '<p>Hướng dẫn thêm và quản lý tài khoản ngân hàng.</p>'
            ),
            array(
                'slug' => 'xem-giao-dich',
                'title' => 'Xem giao dịch',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 3,
                'icon' => '',
                'content' => '<p>Hướng dẫn xem và theo dõi các giao dịch.</p>'
            ),
            array(
                'slug' => 'nguoi-dung-phan-quyen',
                'title' => 'Người dùng & Phân quyền',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 4,
                'icon' => '',
                'content' => '<p>Quản lý người dùng và phân quyền trong hệ thống.</p>'
            ),
            array(
                'slug' => 'tai-khoan-phu',
                'title' => 'Tài khoản phụ',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 5,
                'icon' => '',
                'content' => '<p>Hướng dẫn tạo và quản lý tài khoản phụ.</p>'
            ),
            array(
                'slug' => 'cau-hinh-tk-ngan-hang',
                'title' => 'Cấu hình TK ngân hàng',
                'parent_slug' => 'huong-dan-chung',
                'menu_order' => 6,
                'icon' => '',
                'content' => '<p>Cấu hình chi tiết tài khoản ngân hàng.</p>'
            ),

            // Cấu hình công ty children
            array(
                'slug' => 'goi-dich-vu-cty',
                'title' => 'Gói dịch vụ',
                'parent_slug' => 'cau-hinh-cong-ty',
                'menu_order' => 1,
                'icon' => '',
                'content' => '<p>Quản lý gói dịch vụ dành cho doanh nghiệp.</p>'
            ),
            array(
                'slug' => 'hoa-don-thanh-toan',
                'title' => 'Hóa đơn & thanh toán',
                'parent_slug' => 'cau-hinh-cong-ty',
                'menu_order' => 2,
                'icon' => '',
                'content' => '<p>Quản lý hóa đơn và thanh toán.</p>'
            ),
            array(
                'slug' => 'cau-hinh-chung',
                'title' => 'Cấu hình chung',
                'parent_slug' => 'cau-hinh-cong-ty',
                'menu_order' => 3,
                'icon' => '',
                'content' => '<p>Các cấu hình chung cho công ty.</p>'
            ),

            // Chia sẻ biến động số dư children
            array(
                'slug' => 'tich-hop-telegram',
                'title' => 'Tích hợp Telegram ✓',
                'parent_slug' => 'chia-se-bien-dong-so-du',
                'menu_order' => 1,
                'icon' => 'fab fa-telegram',
                'content' => '<p>Hướng dẫn tích hợp với Telegram để nhận thông báo.</p>'
            ),
            array(
                'slug' => 'tich-hop-lark-messenger',
                'title' => 'Tích hợp Lark Messenger ✓',
                'parent_slug' => 'chia-se-bien-dong-so-du',
                'menu_order' => 2,
                'icon' => 'fas fa-comment',
                'content' => '<p>Hướng dẫn tích hợp với Lark Messenger.</p>'
            ),
            array(
                'slug' => 'tich-hop-viber',
                'title' => 'Tích hợp Viber ✓',
                'parent_slug' => 'chia-se-bien-dong-so-du',
                'menu_order' => 3,
                'icon' => 'fab fa-viber',
                'content' => '<p>Hướng dẫn tích hợp với Viber.</p>'
            ),
            array(
                'slug' => 'mobile-app',
                'title' => 'Mobile App ✓',
                'parent_slug' => 'chia-se-bien-dong-so-du',
                'menu_order' => 4,
                'icon' => 'fas fa-mobile-alt',
                'content' => '<p>Hướng dẫn sử dụng ứng dụng di động.</p>'
            )
        );

        foreach ($child_pages as $page) {
            $parent_id = isset($parent_ids[$page['parent_slug']]) ? $parent_ids[$page['parent_slug']] : 0;

            HuongDan_Database::create_page(
                $page['slug'],
                $page['title'],
                $page['content'],
                $page['menu_order'],
                $parent_id,
                0,
                1,
                $page['icon']
            );
        }
    }

}

// Initialize the plugin
new HuongDanPlugin();