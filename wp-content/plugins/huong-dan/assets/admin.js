jQuery(document).ready(function($) {
    
    // Initialize sortable menu
    if ($('#menu-sortable').length) {
        $('#menu-sortable').sortable({
            handle: '.menu-item',
            placeholder: 'ui-sortable-placeholder',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            start: function(event, ui) {
                ui.placeholder.height(ui.item.height());
            },
            update: function(event, ui) {
                // Menu order has changed
                console.log('Menu order changed');
            }
        });
    }
    
    // Save menu order
    $('#save-menu-order').on('click', function() {
        var button = $(this);
        var status = $('#save-status');
        
        button.prop('disabled', true).text('Đang lưu...');
        status.removeClass('success error').text('');
        
        var menuData = [];
        var menuOrder = 0;
        
        $('#menu-sortable .menu-item').each(function() {
            var $item = $(this);
            var itemData = {
                id: parseInt($item.data('id')),
                parent_id: parseInt($item.data('parent-id')) || 0,
                menu_order: menuOrder++,
                depth: parseInt($item.data('depth')) || 0
            };
            
            // Determine parent based on position
            var $prevParent = $item.prevAll('.parent-item').first();
            if ($prevParent.length && $item.hasClass('child-item')) {
                itemData.parent_id = parseInt($prevParent.data('id'));
                itemData.depth = 1;
            } else if ($item.hasClass('parent-item')) {
                itemData.parent_id = 0;
                itemData.depth = 0;
            }
            
            menuData.push(itemData);
        });
        
        // Send AJAX request
        $.ajax({
            url: huongDanAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'huong_dan_update_menu_order',
                menu_data: JSON.stringify(menuData),
                nonce: huongDanAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    status.addClass('success').text('✓ Đã lưu thành công!');
                    setTimeout(function() {
                        status.text('');
                    }, 3000);
                } else {
                    status.addClass('error').text('✗ Lỗi: ' + response.data);
                }
            },
            error: function() {
                status.addClass('error').text('✗ Lỗi kết nối!');
            },
            complete: function() {
                button.prop('disabled', false).text('Lưu thứ tự');
            }
        });
    });
    
    // Enhanced sortable with parent-child logic
    if ($('#menu-sortable').length) {
        $('#menu-sortable').sortable('destroy'); // Remove previous sortable
        
        $('#menu-sortable').sortable({
            handle: '.menu-item',
            placeholder: 'ui-sortable-placeholder menu-item',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            connectWith: '#menu-sortable',
            start: function(event, ui) {
                ui.placeholder.height(ui.item.height());
                ui.item.addClass('ui-sortable-helper');
            },
            stop: function(event, ui) {
                ui.item.removeClass('ui-sortable-helper');
                updateMenuStructure();
            },
            sort: function(event, ui) {
                // Visual feedback during sorting
                var $item = ui.item;
                var $placeholder = ui.placeholder;
                
                // Check if we're over a parent item
                var $target = $placeholder.prev('.menu-item');
                if ($target.hasClass('parent-item')) {
                    $placeholder.addClass('child-placeholder');
                } else {
                    $placeholder.removeClass('child-placeholder');
                }
            }
        });
    }
    
    function updateMenuStructure() {
        var currentParentId = 0;
        
        $('#menu-sortable .menu-item').each(function(index) {
            var $item = $(this);
            
            if ($item.hasClass('parent-item')) {
                currentParentId = $item.data('id');
                $item.attr('data-parent-id', 0);
                $item.attr('data-depth', 0);
            } else if ($item.hasClass('child-item')) {
                // Find the previous parent item
                var $prevParent = $item.prevAll('.parent-item').first();
                if ($prevParent.length) {
                    var parentId = $prevParent.data('id');
                    $item.attr('data-parent-id', parentId);
                    $item.attr('data-depth', 1);
                } else {
                    $item.attr('data-parent-id', 0);
                    $item.attr('data-depth', 0);
                }
            }
        });
    }
    
    // Add visual indicators
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .child-placeholder {
                margin-left: 40px !important;
                background: #e3f2fd !important;
                border-left: 4px solid #2196f3 !important;
            }
            
            .menu-item.ui-sortable-helper {
                transform: rotate(2deg);
                z-index: 1000;
            }
            
            .menu-item:hover {
                background-color: #f8f9fa;
            }
            
            .parent-item:hover {
                background-color: #e9ecef !important;
            }
        `)
        .appendTo('head');
});

// Additional helper functions
function getMenuHierarchy() {
    var hierarchy = [];
    var currentParent = null;
    
    jQuery('#menu-sortable .menu-item').each(function() {
        var $item = jQuery(this);
        var itemData = {
            id: $item.data('id'),
            title: $item.find('.menu-item-title').text().trim(),
            is_parent: $item.hasClass('parent-item'),
            has_link: !$item.hasClass('parent-item')
        };
        
        if (itemData.is_parent) {
            itemData.children = [];
            currentParent = itemData;
            hierarchy.push(itemData);
        } else if (currentParent) {
            currentParent.children.push(itemData);
        } else {
            hierarchy.push(itemData);
        }
    });
    
    return hierarchy;
}

function validateMenuStructure() {
    var isValid = true;
    var errors = [];
    
    // Check if child items have parent items before them
    var hasParent = false;
    jQuery('#menu-sortable .menu-item').each(function() {
        var $item = jQuery(this);
        
        if ($item.hasClass('parent-item')) {
            hasParent = true;
        } else if ($item.hasClass('child-item') && !hasParent) {
            errors.push('Child item "' + $item.find('.menu-item-title').text() + '" needs a parent item before it.');
            isValid = false;
        }
    });
    
    if (!isValid) {
        alert('Menu structure errors:\n' + errors.join('\n'));
    }
    
    return isValid;
}
