jQuery(document).ready(function($) {

    console.log('Admin JS loaded');
    console.log('jQuery UI Sortable available:', typeof $.fn.sortable !== 'undefined');
    console.log('Menu sortable element found:', $('#menu-sortable').length);

    // Initialize sortable menu
    if ($('#menu-sortable').length && typeof $.fn.sortable !== 'undefined') {
        console.log('Initializing sortable...');

        $('#menu-sortable').sortable({
            items: '.menu-item',
            handle: '.menu-item',
            placeholder: 'ui-sortable-placeholder menu-item',
            tolerance: 'pointer',
            cursor: 'move',
            opacity: 0.8,
            axis: 'y',
            containment: 'parent',
            start: function(event, ui) {
                console.log('Drag started');
                ui.placeholder.height(ui.item.height());
                ui.item.addClass('ui-sortable-helper');
            },
            stop: function(event, ui) {
                console.log('Drag stopped');
                ui.item.removeClass('ui-sortable-helper');
                updateMenuStructure();
            },
            update: function(event, ui) {
                console.log('Menu order changed');
            }
        });

        console.log('Sortable initialized successfully');
    } else {
        console.error('Cannot initialize sortable - missing requirements');
        if (!$('#menu-sortable').length) {
            console.error('Menu sortable element not found');
        }
        if (typeof $.fn.sortable === 'undefined') {
            console.error('jQuery UI Sortable not loaded');
        }
    }
    
    // Save menu order
    $('#save-menu-order').on('click', function() {
        var button = $(this);
        var status = $('#save-status');
        
        button.prop('disabled', true).text('Đang lưu...');
        status.removeClass('success error').text('');
        
        var menuData = [];
        var menuOrder = 0;
        
        $('#menu-sortable .menu-item').each(function() {
            var $item = $(this);
            var itemData = {
                id: parseInt($item.data('id')),
                parent_id: parseInt($item.data('parent-id')) || 0,
                menu_order: menuOrder++,
                depth: parseInt($item.data('depth')) || 0
            };
            
            // Determine parent based on position
            var $prevParent = $item.prevAll('.parent-item').first();
            if ($prevParent.length && $item.hasClass('child-item')) {
                itemData.parent_id = parseInt($prevParent.data('id'));
                itemData.depth = 1;
            } else if ($item.hasClass('parent-item')) {
                itemData.parent_id = 0;
                itemData.depth = 0;
            }
            
            menuData.push(itemData);
        });
        
        // Send AJAX request
        $.ajax({
            url: huongDanAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'huong_dan_update_menu_order',
                menu_data: JSON.stringify(menuData),
                nonce: huongDanAjax.nonce
            },
            success: function(response) {
                if (response.success) {
                    status.addClass('success').text('✓ Đã lưu thành công!');
                    setTimeout(function() {
                        status.text('');
                    }, 3000);
                } else {
                    status.addClass('error').text('✗ Lỗi: ' + response.data);
                }
            },
            error: function() {
                status.addClass('error').text('✗ Lỗi kết nối!');
            },
            complete: function() {
                button.prop('disabled', false).text('Lưu thứ tự');
            }
        });
    });
    
    // Test sortable functionality
    function testSortable() {
        if ($('#menu-sortable').length && typeof $.fn.sortable !== 'undefined') {
            console.log('Testing sortable functionality...');

            // Simple test
            $('#menu-sortable').sortable({
                items: '.menu-item',
                cursor: 'move',
                opacity: 0.8,
                placeholder: 'ui-sortable-placeholder',
                start: function(event, ui) {
                    console.log('Sortable start event triggered');
                    ui.placeholder.height(ui.item.height());
                },
                update: function(event, ui) {
                    console.log('Sortable update event triggered');
                },
                stop: function(event, ui) {
                    console.log('Sortable stop event triggered');
                }
            });

            console.log('Sortable test completed');
        } else {
            console.error('Cannot test sortable - requirements not met');
        }
    }

    // Run test
    testSortable();

    // Fallback: Manual drag and drop implementation
    function initManualDragDrop() {
        console.log('Initializing manual drag and drop...');

        var draggedElement = null;
        var placeholder = null;

        $('.menu-item').each(function() {
            var $item = $(this);

            // Make items draggable
            $item.attr('draggable', true);

            $item.on('dragstart', function(e) {
                draggedElement = this;
                $(this).addClass('dragging');
                console.log('Drag started for:', $(this).find('.menu-item-title').text());

                // Create placeholder
                placeholder = $('<div class="drag-placeholder">Drop here</div>');

                e.originalEvent.dataTransfer.effectAllowed = 'move';
                e.originalEvent.dataTransfer.setData('text/html', this.outerHTML);
            });

            $item.on('dragend', function(e) {
                $(this).removeClass('dragging');
                if (placeholder) {
                    placeholder.remove();
                    placeholder = null;
                }
                draggedElement = null;
                console.log('Drag ended');
            });

            $item.on('dragover', function(e) {
                e.preventDefault();
                e.originalEvent.dataTransfer.dropEffect = 'move';

                if (draggedElement && draggedElement !== this) {
                    var $this = $(this);
                    var rect = this.getBoundingClientRect();
                    var midpoint = rect.top + rect.height / 2;

                    if (e.originalEvent.clientY < midpoint) {
                        $this.before(placeholder);
                    } else {
                        $this.after(placeholder);
                    }
                }
            });

            $item.on('drop', function(e) {
                e.preventDefault();

                if (draggedElement && draggedElement !== this) {
                    var $draggedElement = $(draggedElement);
                    var $this = $(this);

                    // Determine drop position
                    var rect = this.getBoundingClientRect();
                    var midpoint = rect.top + rect.height / 2;

                    if (e.originalEvent.clientY < midpoint) {
                        $this.before($draggedElement);
                    } else {
                        $this.after($draggedElement);
                    }

                    console.log('Item dropped');
                    updateMenuStructure();
                }

                if (placeholder) {
                    placeholder.remove();
                    placeholder = null;
                }
            });
        });

        // Add CSS for manual drag and drop
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .menu-item[draggable="true"] {
                    cursor: move;
                }

                .menu-item.dragging {
                    opacity: 0.5;
                    transform: rotate(2deg);
                }

                .drag-placeholder {
                    height: 4px;
                    background: #007cba;
                    margin: 2px 0;
                    border-radius: 2px;
                    animation: pulse 1s infinite;
                }

                @keyframes pulse {
                    0% { opacity: 0.5; }
                    50% { opacity: 1; }
                    100% { opacity: 0.5; }
                }
            `)
            .appendTo('head');
    }

    // Initialize manual drag and drop as fallback
    setTimeout(function() {
        if (!$('#menu-sortable').hasClass('ui-sortable')) {
            console.log('jQuery UI Sortable not working, using manual drag and drop');
            initManualDragDrop();
        }
    }, 1000);
    
    function updateMenuStructure() {
        var currentParentId = 0;
        
        $('#menu-sortable .menu-item').each(function(index) {
            var $item = $(this);
            
            if ($item.hasClass('parent-item')) {
                currentParentId = $item.data('id');
                $item.attr('data-parent-id', 0);
                $item.attr('data-depth', 0);
            } else if ($item.hasClass('child-item')) {
                // Find the previous parent item
                var $prevParent = $item.prevAll('.parent-item').first();
                if ($prevParent.length) {
                    var parentId = $prevParent.data('id');
                    $item.attr('data-parent-id', parentId);
                    $item.attr('data-depth', 1);
                } else {
                    $item.attr('data-parent-id', 0);
                    $item.attr('data-depth', 0);
                }
            }
        });
    }
    
    // Add visual indicators
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .child-placeholder {
                margin-left: 40px !important;
                background: #e3f2fd !important;
                border-left: 4px solid #2196f3 !important;
            }
            
            .menu-item.ui-sortable-helper {
                transform: rotate(2deg);
                z-index: 1000;
            }
            
            .menu-item:hover {
                background-color: #f8f9fa;
            }
            
            .parent-item:hover {
                background-color: #e9ecef !important;
            }
        `)
        .appendTo('head');
});

// Additional helper functions
function getMenuHierarchy() {
    var hierarchy = [];
    var currentParent = null;
    
    jQuery('#menu-sortable .menu-item').each(function() {
        var $item = jQuery(this);
        var itemData = {
            id: $item.data('id'),
            title: $item.find('.menu-item-title').text().trim(),
            is_parent: $item.hasClass('parent-item'),
            has_link: !$item.hasClass('parent-item')
        };
        
        if (itemData.is_parent) {
            itemData.children = [];
            currentParent = itemData;
            hierarchy.push(itemData);
        } else if (currentParent) {
            currentParent.children.push(itemData);
        } else {
            hierarchy.push(itemData);
        }
    });
    
    return hierarchy;
}

function validateMenuStructure() {
    var isValid = true;
    var errors = [];
    
    // Check if child items have parent items before them
    var hasParent = false;
    jQuery('#menu-sortable .menu-item').each(function() {
        var $item = jQuery(this);
        
        if ($item.hasClass('parent-item')) {
            hasParent = true;
        } else if ($item.hasClass('child-item') && !hasParent) {
            errors.push('Child item "' + $item.find('.menu-item-title').text() + '" needs a parent item before it.');
            isValid = false;
        }
    });
    
    if (!isValid) {
        alert('Menu structure errors:\n' + errors.join('\n'));
    }
    
    return isValid;
}
