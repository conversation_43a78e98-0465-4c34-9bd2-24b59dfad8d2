// Frontend JavaScript for Huong Dan plugin

jQuery(document).ready(function($) {
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        
        var target = this.hash;
        var $target = $(target);
        
        if ($target.length) {
            $('html, body').animate({
                scrollTop: $target.offset().top - 100
            }, 500, 'swing');
        }
    });
    
    // Highlight current section in sidebar
    function highlightCurrentSection() {
        var scrollTop = $(window).scrollTop();
        var windowHeight = $(window).height();
        
        $('.content-area h2, .content-area h3').each(function() {
            var $this = $(this);
            var elementTop = $this.offset().top;
            var elementBottom = elementTop + $this.outerHeight();
            
            if (elementTop <= scrollTop + 200 && elementBottom >= scrollTop) {
                var id = $this.attr('id');
                if (id) {
                    $('.sidebar-menu a').removeClass('current-section');
                    $('.sidebar-menu a[href="#' + id + '"]').addClass('current-section');
                }
            }
        });
    }
    
    // Add scroll spy functionality
    $(window).on('scroll', function() {
        highlightCurrentSection();
    });
    
    // Add copy code functionality to code blocks
    $('.content-area pre').each(function() {
        var $pre = $(this);
        var $button = $('<button class="copy-code-btn" title="Copy code">📋</button>');
        
        $pre.css('position', 'relative');
        $button.css({
            'position': 'absolute',
            'top': '10px',
            'right': '10px',
            'background': '#007bff',
            'color': 'white',
            'border': 'none',
            'padding': '5px 8px',
            'border-radius': '3px',
            'cursor': 'pointer',
            'font-size': '12px',
            'z-index': '10'
        });
        
        $pre.append($button);
        
        $button.on('click', function() {
            var code = $pre.find('code').length ? $pre.find('code').text() : $pre.text();
            
            // Create temporary textarea to copy text
            var $temp = $('<textarea>');
            $('body').append($temp);
            $temp.val(code).select();
            document.execCommand('copy');
            $temp.remove();
            
            // Show feedback
            $button.text('✓ Copied!');
            setTimeout(function() {
                $button.text('📋');
            }, 2000);
        });
    });
    
    // Add table of contents for long pages
    function generateTableOfContents() {
        var $headings = $('.content-area h2, .content-area h3');
        
        if ($headings.length > 3) {
            var $toc = $('<div class="table-of-contents"><h4>Mục lục</h4><ul></ul></div>');
            var $tocList = $toc.find('ul');
            
            $headings.each(function(index) {
                var $heading = $(this);
                var id = 'heading-' + index;
                var text = $heading.text();
                var level = $heading.prop('tagName').toLowerCase();
                
                $heading.attr('id', id);
                
                var $li = $('<li><a href="#' + id + '">' + text + '</a></li>');
                if (level === 'h3') {
                    $li.addClass('toc-sub');
                }
                
                $tocList.append($li);
            });
            
            // Insert TOC after the first paragraph or at the beginning
            var $firstP = $('.content-area p').first();
            if ($firstP.length) {
                $firstP.after($toc);
            } else {
                $('.content-area h1').after($toc);
            }
        }
    }
    
    // Generate TOC if needed
    generateTableOfContents();
    
    // Add search functionality to sidebar
    function addSidebarSearch() {
        var $searchBox = $('<div class="sidebar-search"><input type="text" placeholder="Tìm kiếm..." class="form-control form-control-sm"></div>');
        
        $searchBox.css({
            'padding': '15px',
            'border-bottom': '1px solid #dee2e6',
            'background': '#f8f9fa'
        });
        
        $('.sidebar').first().prepend($searchBox);
        
        var $searchInput = $searchBox.find('input');
        
        $searchInput.on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            $('.sidebar-menu a').each(function() {
                var $link = $(this);
                var text = $link.text().toLowerCase();
                var $li = $link.closest('li');
                
                if (text.includes(searchTerm) || searchTerm === '') {
                    $li.show();
                } else {
                    $li.hide();
                }
            });
            
            // Hide empty sidebar sections
            $('.sidebar').each(function() {
                var $sidebar = $(this);
                var visibleItems = $sidebar.find('.sidebar-menu li:visible').length;
                
                if (visibleItems === 0 && searchTerm !== '') {
                    $sidebar.hide();
                } else {
                    $sidebar.show();
                }
            });
        });
    }
    
    // Add search functionality
    addSidebarSearch();
    
    // Add print functionality
    function addPrintButton() {
        var $printBtn = $('<button class="btn btn-outline-secondary btn-sm print-btn" style="margin-bottom: 20px;"><i class="fas fa-print"></i> In trang</button>');
        
        $('.content-area h1').after($printBtn);
        
        $printBtn.on('click', function() {
            window.print();
        });
    }
    
    // Add print button
    addPrintButton();
    
    // Add back to top button
    function addBackToTop() {
        var $backToTop = $('<button class="back-to-top" title="Lên đầu trang">↑</button>');
        
        $backToTop.css({
            'position': 'fixed',
            'bottom': '30px',
            'right': '30px',
            'width': '50px',
            'height': '50px',
            'background': '#007bff',
            'color': 'white',
            'border': 'none',
            'border-radius': '50%',
            'cursor': 'pointer',
            'font-size': '20px',
            'z-index': '1000',
            'display': 'none',
            'box-shadow': '0 2px 10px rgba(0,0,0,0.3)',
            'transition': 'all 0.3s ease'
        });
        
        $('body').append($backToTop);
        
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        });
        
        $backToTop.on('click', function() {
            $('html, body').animate({scrollTop: 0}, 500);
        });
        
        $backToTop.on('mouseenter', function() {
            $(this).css('transform', 'scale(1.1)');
        }).on('mouseleave', function() {
            $(this).css('transform', 'scale(1)');
        });
    }
    
    // Add back to top button
    addBackToTop();
    
    // Add loading states
    function showLoading($element) {
        $element.addClass('loading');
    }
    
    function hideLoading($element) {
        $element.removeClass('loading');
    }
    
    // Handle page navigation with loading states
    $('.sidebar-menu a').on('click', function() {
        showLoading($('.content-area'));
    });
    
    // Add keyboard navigation
    $(document).on('keydown', function(e) {
        // Alt + Left Arrow: Previous page
        if (e.altKey && e.keyCode === 37) {
            var $prevLink = $('.sidebar-menu a.active').closest('li').prev().find('a');
            if ($prevLink.length) {
                window.location.href = $prevLink.attr('href');
            }
        }
        
        // Alt + Right Arrow: Next page
        if (e.altKey && e.keyCode === 39) {
            var $nextLink = $('.sidebar-menu a.active').closest('li').next().find('a');
            if ($nextLink.length) {
                window.location.href = $nextLink.attr('href');
            }
        }
    });
    
    // Add CSS for table of contents
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .table-of-contents {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 20px;
                margin: 20px 0;
            }
            
            .table-of-contents h4 {
                margin-top: 0;
                margin-bottom: 15px;
                color: #495057;
                font-size: 1.1rem;
            }
            
            .table-of-contents ul {
                list-style: none;
                padding-left: 0;
                margin-bottom: 0;
            }
            
            .table-of-contents li {
                margin-bottom: 8px;
            }
            
            .table-of-contents li.toc-sub {
                padding-left: 20px;
                font-size: 0.9rem;
            }
            
            .table-of-contents a {
                color: #007bff;
                text-decoration: none;
                transition: color 0.3s;
            }
            
            .table-of-contents a:hover {
                color: #0056b3;
                text-decoration: underline;
            }
            
            .sidebar-menu a.current-section {
                background-color: #e3f2fd !important;
                color: #1976d2 !important;
                border-left: 3px solid #1976d2;
            }
        `)
        .appendTo('head');
});

// Utility functions
function scrollToElement(selector, offset = 100) {
    var $element = $(selector);
    if ($element.length) {
        $('html, body').animate({
            scrollTop: $element.offset().top - offset
        }, 500);
    }
}

function copyToClipboard(text) {
    var $temp = $('<textarea>');
    $('body').append($temp);
    $temp.val(text).select();
    document.execCommand('copy');
    $temp.remove();
}
