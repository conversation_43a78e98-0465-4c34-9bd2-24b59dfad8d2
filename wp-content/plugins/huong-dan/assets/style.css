/* Frontend styles for <PERSON><PERSON> plugin */

/* Additional custom styles for the guide pages */
.sidebar {
    transition: all 0.3s ease;
}

.sidebar:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.sidebar-header {
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.sidebar:hover .sidebar-header::before {
    left: 100%;
}

.sidebar-menu a {
    position: relative;
    overflow: hidden;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,123,255,0.1), transparent);
    transition: left 0.3s;
}

.sidebar-menu a:hover::before {
    left: 100%;
}

.sidebar-menu a.active {
    position: relative;
}

.sidebar-menu a.active::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #ffd700;
}

/* Content area enhancements */
.content-area {
    position: relative;
}

.content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 8px 8px 0 0;
}

/* Improved typography */
.content-area h1 {
    position: relative;
    padding-left: 20px;
}

.content-area h1::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Enhanced lists */
.content-area ul li {
    position: relative;
    padding-left: 10px;
}

.content-area ul li::before {
    content: '▶';
    position: absolute;
    left: -10px;
    color: #007bff;
    font-size: 0.8em;
}

/* Code blocks */
.content-area pre {
    position: relative;
    border-left: 4px solid #007bff;
}

.content-area pre::before {
    content: 'CODE';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 10px;
    color: #666;
    background: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #ddd;
}

/* Blockquotes */
.content-area blockquote {
    position: relative;
    background: #f8f9fa;
    border-radius: 0 8px 8px 0;
    margin: 1.5rem 0;
    padding: 1rem 1rem 1rem 2rem;
}

.content-area blockquote::before {
    content: '"';
    position: absolute;
    left: 10px;
    top: 5px;
    font-size: 2em;
    color: #007bff;
    font-family: serif;
}

/* Breadcrumb enhancements */
.breadcrumb-custom {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 10px 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
    content: '›';
    color: #007bff;
    font-weight: bold;
}

/* Responsive improvements */
@media (max-width: 992px) {
    .sidebar {
        margin-bottom: 1.5rem;
    }
    
    .content-area {
        margin-top: 0;
    }
}

@media (max-width: 768px) {
    .header .logo {
        font-size: 1.2rem;
    }
    
    .sidebar-header {
        font-size: 0.8rem;
        padding: 0.8rem;
    }
    
    .sidebar-menu a {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
    
    .content-area {
        padding: 1.5rem;
    }
    
    .content-area h1 {
        font-size: 1.5rem;
        padding-left: 15px;
    }
    
    .content-area h1::before {
        width: 3px;
    }
}

/* Loading states */
.sidebar.loading {
    opacity: 0.7;
    pointer-events: none;
}

.content-area.loading {
    opacity: 0.7;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print styles */
@media print {
    .header,
    .sidebar,
    .breadcrumb-custom {
        display: none;
    }
    
    .content-area {
        box-shadow: none;
        border: none;
        padding: 0;
        margin: 0;
    }
    
    .content-area::before {
        display: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .sidebar {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .sidebar-header {
        background: #404040;
        color: #e0e0e0;
        border-color: #555;
    }
    
    .sidebar-menu a {
        color: #e0e0e0;
    }
    
    .sidebar-menu a:hover {
        background-color: #404040;
        color: #007bff;
    }
    
    .content-area {
        background: #2d2d2d;
        border-color: #404040;
        color: #e0e0e0;
    }
    
    .content-area h1,
    .content-area h2,
    .content-area h3 {
        color: #f0f0f0;
    }
    
    .content-area code {
        background-color: #404040;
        color: #e0e0e0;
    }
    
    .content-area pre {
        background-color: #404040;
        border-color: #007bff;
    }
    
    .content-area blockquote {
        background: #404040;
        border-color: #007bff;
        color: #e0e0e0;
    }
    
    .breadcrumb-custom {
        background: linear-gradient(135deg, #404040, #555);
        border-color: #666;
    }
}
