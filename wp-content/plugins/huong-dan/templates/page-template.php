<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo esc_html($page->title); ?> - Hướng dẫn SePay</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            background-color: #f5f7fa;
            line-height: 1.6;
            color: #333;
        }

        .header {
            background: #fff;
            border-bottom: 1px solid #e1e8ed;
            padding: 0.75rem 1rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            z-index: 1000;
        }

        .header .logo {
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            color: #1976d2;
        }

        .header .logo i {
            margin-right: 0.5rem;
            color: #1976d2;
            font-size: 1.5rem;
        }

        .header-links {
            display: flex;
            gap: 1.5rem;
            align-items: center;
            justify-content: flex-end;
        }

        .header-links a {
            color: #666;
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.3s;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .header-links a:hover {
            color: #1976d2;
        }

        .header-links a i {
            font-size: 0.875rem;
        }
        
        .sidebar {
            background: white;
            border-radius: 0;
            box-shadow: none;
            border-right: 1px solid #e1e8ed;
            padding: 0;
            height: calc(100vh - 60px);
            position: fixed;
            top: 60px;
            left: 0;
            width: 250px;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 100;
            display: block !important;
            visibility: visible !important;
        }

        .sidebar-header {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e1e8ed;
            font-weight: 600;
            color: #657786;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: block !important;
            visibility: visible !important;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;
            display: block !important;
            visibility: visible !important;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid #f1f3f4;
            position: relative;
        }
        
        .sidebar-menu li:last-child {
            border-bottom: none;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #14171a;
            text-decoration: none;
            transition: background-color 0.2s, color 0.2s;
            font-size: 0.875rem;
            font-weight: 400;
            position: relative;
        }

        .sidebar-menu a:hover {
            background-color: #f7f9fa;
            color: #1976d2;
            transform: none;
            transition: background-color 0.2s, color 0.2s;
        }

        .sidebar-menu a.active {
            background-color: #1976d2;
            color: white;
            font-weight: 500;
        }

        /* Prevent layout shift on hover */
        .sidebar-menu a::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: transparent;
            transition: background-color 0.2s;
        }

        .sidebar-menu a:hover::before {
            background-color: #1976d2;
        }

        .sidebar-menu a.active::before {
            background-color: #1565c0;
        }
        
        .sidebar-menu a i {
            width: 18px;
            margin-right: 0.5rem;
            text-align: center;
            font-size: 0.875rem;
        }

        .content-area {
            background: white;
            border-radius: 0;
            box-shadow: none;
            border: none;
            padding: 2rem;
            min-height: calc(100vh - 60px);
            margin-left: 250px;
        }

        .content-area h1 {
            color: #14171a;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e1e8ed;
            font-size: 1.75rem;
            font-weight: 600;
        }
        
        .content-area h2 {
            color: #14171a;
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-size: 1.375rem;
            font-weight: 600;
        }

        .content-area h3 {
            color: #14171a;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .content-area p {
            line-height: 1.6;
            margin-bottom: 1rem;
            color: #000;
        }
        
        .content-area ul, .content-area ol {
            margin-bottom: 1rem;
            padding-left: 2rem;
        }
        
        .content-area li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        
        .content-area code {
            background-color: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.9em;
        }
        
        .content-area pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
        }
        
        .content-area blockquote {
            border-left: 4px solid #007bff;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #6c757d;
        }
        
        .breadcrumb-nav {
            position: sticky;
            top: 0;
            z-index: 10;
            background: white;
            border-bottom: 1px solid #e1e8ed;
            margin: -2rem -2rem 1.5rem -2rem;
            padding: 0 2rem;
        }

        .breadcrumb-custom {
            background: transparent;
            padding: 0.75rem 0;
            margin-bottom: 0;
            border-radius: 0;
            border: none;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: #1976d2;
            text-decoration: none;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .breadcrumb-custom .breadcrumb-item a:hover {
            color: #1565c0;
            text-decoration: underline;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: #657786;
            font-size: 0.875rem;
        }

        .breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #657786;
            margin: 0 0.5rem;
        }

        .breadcrumb-custom .breadcrumb-item i {
            font-size: 0.75rem;
        }
        
        .main-container {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .main-content {
            background: #f5f7fa;
            margin: 0;
            min-height: calc(100vh - 60px);
        }

        /* Additional styling to match the design */
        .page-content {
            font-size: 0.9rem;
            line-height: 1.7;
        }

        .page-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 1rem 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .page-content ul {
            padding-left: 1.5rem;
        }

        .page-content li {
            margin-bottom: 0.5rem;
        }

        .page-content strong {
            color: #14171a;
            font-weight: 600;
        }

        .page-content code {
            background: #f7f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.85rem;
            color: #1976d2;
            border: 1px solid #e1e8ed;
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #e1e8ed;
            margin: 1rem 0;
        }

        .alert-info {
            background: #e3f2fd;
            border-color: #bbdefb;
            color: #1565c0;
        }

        .alert-link {
            color: #0d47a1;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 60px;
                left: -250px;
                transition: left 0.3s ease;
                z-index: 1001;
            }

            .sidebar.show {
                left: 0;
            }

            .content-area {
                margin-left: 0;
                padding: 1rem;
            }

            .header-links {
                display: none;
            }

            .mobile-menu-toggle {
                display: block;
                background: none;
                border: none;
                color: #1976d2;
                font-size: 1.25rem;
                cursor: pointer;
            }

            .overlay {
                position: fixed;
                top: 60px;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                z-index: 1000;
                display: none;
            }

            .overlay.show {
                display: block;
            }
        }

        .mobile-menu-toggle {
            display: none;
            margin-right: 1rem;
        }

        .mobile-menu-toggle:focus {
            outline: none;
        }
    </style>
    
    <?php wp_head(); ?>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="d-flex align-items-center justify-content-between h-100">
            <div class="d-flex align-items-center">
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <i class="fas fa-diamond"></i>
                    SePay
                </div>
            </div>
            <div class="header-links">
                <a href="#"><i class="fas fa-life-ring"></i> Support</a>
                <a href="#"><i class="fab fa-youtube"></i> Youtube</a>
                <a href="#"><i class="fab fa-facebook"></i> Facebook</a>
                <a href="#"><i class="fab fa-telegram"></i> Telegram</a>
            </div>
        </div>
    </div>

    <!-- Mobile Overlay -->
    <div class="overlay" onclick="toggleMobileMenu()"></div>

    <!-- Sidebar -->
    <div class="sidebar">
        <?php HuongDan_Frontend::render_sidebar_menu($page->slug); ?>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-area">
                    <!-- Breadcrumb -->
                    <nav aria-label="breadcrumb" class="breadcrumb-nav">
                        <ol class="breadcrumb breadcrumb-custom">
                            <li class="breadcrumb-item">
                                <a href="/huong-dan/">
                                    <i class="fas fa-home"></i> Hướng dẫn
                                </a>
                            </li>
                            <?php if ($page->parent_id > 0): ?>
                                <?php $parent = HuongDan_Database::get_page_by_id($page->parent_id); ?>
                                <?php if ($parent): ?>
                                    <li class="breadcrumb-item">
                                        <a href="/huong-dan/<?php echo esc_attr($parent->slug); ?>">
                                            <?php echo esc_html($parent->title); ?>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                            <li class="breadcrumb-item active" aria-current="page">
                                <?php echo esc_html($page->title); ?>
                            </li>
                        </ol>
                    </nav>
                    
                    <!-- Page Title -->
                    <h1><?php echo esc_html($page->title); ?></h1>
                    
                    <!-- Page Content -->
                    <div class="page-content">
                        <?php if (!empty($page->content)): ?>
                            <?php echo wp_kses_post($page->content); ?>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Nội dung trang này chưa được cập nhật.
                                <?php if (current_user_can('manage_options')): ?>
                                    <a href="<?php echo admin_url('admin.php?page=huong-dan-' . $page->slug); ?>" class="alert-link">Chỉnh sửa ngay</a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    function toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.overlay');

        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
    }

    // Close mobile menu when clicking on a menu item
    document.querySelectorAll('.sidebar-menu a').forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 768) {
                toggleMobileMenu();
            }
        });
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            document.querySelector('.sidebar').classList.remove('show');
            document.querySelector('.overlay').classList.remove('show');
        }
    });
    </script>

    <?php wp_footer(); ?>
</body>
</html>
