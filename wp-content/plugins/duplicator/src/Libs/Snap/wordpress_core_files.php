<?php

/**
 * Core wordpress file list
 *
 * @package   Duplicator
 * @copyright (c) 2022, Snap Creek LLC
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

defined('ABSPATH') || defined('DUPXABSPATH') || exit;

/*
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 *
 * >>>>>> THIS FILE IS GENERATED WITH A SCRIPT, DON'T EDIT IT DIRECTLY <<<<<
 * >>>>>> USE THE GENERATOR SCRIPT <<<<<
 */

// @phpstan-ignore-next-line
self::$corePathList = array(
    'wp-login.php'         => "f",
    'wp-comments-post.php' => "f",
    'wp-signup.php'        => "f",
    'wp-cron.php'          => "f",
    'wp-blog-header.php'   => "f",
    'readme.html'          => "f",
    'wp-settings.php'      => "f",
    'index.php'            => "f",
    'wp-admin'             => array(
        'edit-form-advanced.php'    => "f",
        'media-upload.php'          => "f",
        'menu.php'                  => "f",
        'site-health-info.php'      => "f",
        'user-edit.php'             => "f",
        'privacy-policy-guide.php'  => "f",
        'async-upload.php'          => "f",
        'customize.php'             => "f",
        'tools.php'                 => "f",
        'about.php'                 => "f",
        'update-core.php'           => "f",
        'revision.php'              => "f",
        'ms-edit.php'               => "f",
        'site-editor.php'           => "f",
        'media-new.php'             => "f",
        'options-writing.php'       => "f",
        'term.php'                  => "f",
        'widgets-form.php'          => "f",
        'network'                   => array(
            'menu.php'           => "f",
            'site-new.php'       => "f",
            'user-edit.php'      => "f",
            'settings.php'       => "f",
            'about.php'          => "f",
            'update-core.php'    => "f",
            'contribute.php'     => "f",
            'theme-install.php'  => "f",
            'plugin-install.php' => "f",
            'site-settings.php'  => "f",
            'theme-editor.php'   => "f",
            'site-users.php'     => "f",
            'admin.php'          => "f",
            'credits.php'        => "f",
            'update.php'         => "f",
            'index.php'          => "f",
            'privacy.php'        => "f",
            'upgrade.php'        => "f",
            'users.php'          => "f",
            'plugins.php'        => "f",
            'edit.php'           => "f",
            'freedoms.php'       => "f",
            'plugin-editor.php'  => "f",
            'setup.php'          => "f",
            'site-themes.php'    => "f",
            'user-new.php'       => "f",
            'sites.php'          => "f",
            'site-info.php'      => "f",
            'profile.php'        => "f",
            'themes.php'         => "f",
        ),
        'ms-delete-site.php'        => "f",
        'contribute.php'            => "f",
        'theme-install.php'         => "f",
        'link.php'                  => "f",
        'post.php'                  => "f",
        'network.php'               => "f",
        'ms-options.php'            => "f",
        'user'                      => array(
            'menu.php'       => "f",
            'user-edit.php'  => "f",
            'about.php'      => "f",
            'contribute.php' => "f",
            'admin.php'      => "f",
            'credits.php'    => "f",
            'index.php'      => "f",
            'privacy.php'    => "f",
            'freedoms.php'   => "f",
            'profile.php'    => "f",
        ),
        'upload.php'                => "f",
        'plugin-install.php'        => "f",
        'options.php'               => "f",
        'ms-upgrade-network.php'    => "f",
        'custom-background.php'     => "f",
        'theme-editor.php'          => "f",
        'edit-tags.php'             => "f",
        'custom-header.php'         => "f",
        'admin.php'                 => "f",
        'options-privacy.php'       => "f",
        'options-discussion.php'    => "f",
        'credits.php'               => "f",
        'press-this.php'            => "f",
        'my-sites.php'              => "f",
        'ms-sites.php'              => "f",
        'import.php'                => "f",
        'update.php'                => "f",
        'upgrade-functions.php'     => "f",
        'moderation.php'            => "f",
        'options-media.php'         => "f",
        'edit-form-comment.php'     => "f",
        'index.php'                 => "f",
        'admin-ajax.php'            => "f",
        'setup-config.php'          => "f",
        'privacy.php'               => "f",
        'upgrade.php'               => "f",
        'css'                       => array(
            'customize-nav-menus.min.css'     => "f",
            'press-this.css'                  => "f",
            'farbtastic.min.css'              => "f",
            'ie-rtl.css'                      => "f",
            'customize-controls-rtl.min.css'  => "f",
            'themes-rtl.css'                  => "f",
            'common-rtl.min.css'              => "f",
            'deprecated-media.css'            => "f",
            'farbtastic-rtl.min.css'          => "f",
            'forms-rtl.css'                   => "f",
            'press-this.min.css'              => "f",
            'farbtastic.css'                  => "f",
            'color-picker-rtl.css'            => "f",
            'widgets.css'                     => "f",
            'customize-controls-rtl.css'      => "f",
            'edit.css'                        => "f",
            'media.min.css'                   => "f",
            'login-rtl.css'                   => "f",
            'forms.min.css'                   => "f",
            'widgets-rtl.min.css'             => "f",
            'l10n.css'                        => "f",
            'wp-admin-rtl.min.css'            => "f",
            'l10n.min.css'                    => "f",
            'revisions.min.css'               => "f",
            'admin-menu.min.css'              => "f",
            'common.min.css'                  => "f",
            'about-rtl.min.css'               => "f",
            'nav-menus-rtl.min.css'           => "f",
            'dashboard-rtl.min.css'           => "f",
            'customize-controls.css'          => "f",
            'customize-widgets-rtl.min.css'   => "f",
            'admin-menu-rtl.css'              => "f",
            'dashboard-rtl.css'               => "f",
            'color-picker.min.css'            => "f",
            'deprecated-media-rtl.css'        => "f",
            'about.min.css'                   => "f",
            'media-rtl.css'                   => "f",
            'site-icon.min.css'               => "f",
            'wp-admin-rtl.css'                => "f",
            'media-rtl.min.css'               => "f",
            'install.css'                     => "f",
            'admin-menu-rtl.min.css'          => "f",
            'edit-rtl.css'                    => "f",
            'l10n-rtl.css'                    => "f",
            'nav-menus.css'                   => "f",
            'login.css'                       => "f",
            'about-rtl.css'                   => "f",
            'list-tables-rtl.css'             => "f",
            'customize-widgets.min.css'       => "f",
            'wp-admin.min.css'                => "f",
            'forms.css'                       => "f",
            'press-this-editor-rtl.min.css'   => "f",
            'nav-menus-rtl.css'               => "f",
            'press-this-editor-rtl.css'       => "f",
            'customize-nav-menus.css'         => "f",
            'themes.css'                      => "f",
            'press-this-editor.css'           => "f",
            'common.css'                      => "f",
            'revisions-rtl.min.css'           => "f",
            'customize-nav-menus-rtl.min.css' => "f",
            'farbtastic-rtl.css'              => "f",
            'site-health.css'                 => "f",
            'forms-rtl.min.css'               => "f",
            'themes-rtl.min.css'              => "f",
            'site-icon-rtl.min.css'           => "f",
            'nav-menus.min.css'               => "f",
            'edit.min.css'                    => "f",
            'widgets.min.css'                 => "f",
            'list-tables.css'                 => "f",
            'press-this-rtl.css'              => "f",
            'code-editor-rtl.css'             => "f",
            'customize-widgets.css'           => "f",
            'site-health.min.css'             => "f",
            'site-icon.css'                   => "f",
            'install-rtl.min.css'             => "f",
            'revisions.css'                   => "f",
            'color-picker.css'                => "f",
            'install.min.css'                 => "f",
            'list-tables.min.css'             => "f",
            'about.css'                       => "f",
            'site-icon-rtl.css'               => "f",
            'ie-rtl.min.css'                  => "f",
            'customize-controls.min.css'      => "f",
            'customize-nav-menus-rtl.css'     => "f",
            'deprecated-media-rtl.min.css'    => "f",
            'edit-rtl.min.css'                => "f",
            'site-health-rtl.css'             => "f",
            'login.min.css'                   => "f",
            'media.css'                       => "f",
            'themes.min.css'                  => "f",
            'list-tables-rtl.min.css'         => "f",
            'widgets-rtl.css'                 => "f",
            'common-rtl.css'                  => "f",
            'code-editor-rtl.min.css'         => "f",
            'ie.min.css'                      => "f",
            'code-editor.css'                 => "f",
            'site-health-rtl.min.css'         => "f",
            'press-this-rtl.min.css'          => "f",
            'l10n-rtl.min.css'                => "f",
            'dashboard.min.css'               => "f",
            'color-picker-rtl.min.css'        => "f",
            'revisions-rtl.css'               => "f",
            'install-rtl.css'                 => "f",
            'dashboard.css'                   => "f",
            'customize-widgets-rtl.css'       => "f",
            'admin-menu.css'                  => "f",
            'code-editor.min.css'             => "f",
            'wp-admin.css'                    => "f",
            'deprecated-media.min.css'        => "f",
            'colors'                          => array(
                '_mixins.scss'    => "f",
                'ectoplasm'       => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'coffee'          => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                '_admin.scss'     => "f",
                '_variables.scss' => "f",
                'blue'            => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'midnight'        => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'modern'          => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'sunrise'         => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'light'           => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
                'ocean'           => array(
                    'colors.css'         => "f",
                    'colors.scss'        => "f",
                    'colors-rtl.min.css' => "f",
                    'colors-rtl.css'     => "f",
                    'colors.min.css'     => "f",
                ),
            ),
            'ie.css'                          => "f",
            'login-rtl.min.css'               => "f",
            'press-this-editor.min.css'       => "f",
        ),
        'admin-post.php'            => "f",
        'admin-functions.php'       => "f",
        'admin-footer.php'          => "f",
        'erase-personal-data.php'   => "f",
        'edit-link-form.php'        => "f",
        'edit-form-blocks.php'      => "f",
        'authorize-application.php' => "f",
        'options-permalink.php'     => "f",
        'menu-header.php'           => "f",
        'users.php'                 => "f",
        'site-health.php'           => "f",
        'export-personal-data.php'  => "f",
        'plugins.php'               => "f",
        'edit.php'                  => "f",
        'ms-themes.php'             => "f",
        'includes'                  => array(
            'class-wp-press-this.php'                               => "f",
            'menu.php'                                              => "f",
            'class-wp-importer.php'                                 => "f",
            'noop.php'                                              => "f",
            'class-language-pack-upgrader-skin.php'                 => "f",
            'class-custom-image-header.php'                         => "f",
            'class-bulk-theme-upgrader-skin.php'                    => "f",
            'update-core.php'                                       => "f",
            'class-wp-filesystem-direct.php'                        => "f",
            'revision.php'                                          => "f",
            'class-file-upload-upgrader.php'                        => "f",
            'class-automatic-upgrader-skin.php'                     => "f",
            'class-wp-upgrader-skins.php'                           => "f",
            'class-plugin-installer-skin.php'                       => "f",
            'bookmark.php'                                          => "f",
            'ms-deprecated.php'                                     => "f",
            'class-custom-background.php'                           => "f",
            'class-wp-site-health-auto-updates.php'                 => "f",
            'class-wp-privacy-data-removal-requests-list-table.php' => "f",
            'user.php'                                              => "f",
            'class-wp-ajax-upgrader-skin.php'                       => "f",
            'theme-install.php'                                     => "f",
            'ms.php'                                                => "f",
            'post.php'                                              => "f",
            'class-wp-themes-list-table.php'                        => "f",
            'network.php'                                           => "f",
            'class-walker-category-checklist.php'                   => "f",
            'class-walker-nav-menu-edit.php'                        => "f",
            'plugin-install.php'                                    => "f",
            'list-table.php'                                        => "f",
            'meta-boxes.php'                                        => "f",
            'image-edit.php'                                        => "f",
            'options.php'                                           => "f",
            'class-wp-community-events.php'                         => "f",
            'class-theme-installer-skin.php'                        => "f",
            'class-wp-application-passwords-list-table.php'         => "f",
            'dashboard.php'                                         => "f",
            'admin-filters.php'                                     => "f",
            'continents-cities.php'                                 => "f",
            'class-language-pack-upgrader.php'                      => "f",
            'class-wp-screen.php'                                   => "f",
            'admin.php'                                             => "f",
            'class-wp-ms-users-list-table.php'                      => "f",
            'class-theme-upgrader-skin.php'                         => "f",
            'class-wp-media-list-table.php'                         => "f",
            'image.php'                                             => "f",
            'class-core-upgrader.php'                               => "f",
            'class-wp-privacy-requests-table.php'                   => "f",
            'credits.php'                                           => "f",
            'edit-tag-messages.php'                                 => "f",
            'taxonomy.php'                                          => "f",
            'privacy-tools.php'                                     => "f",
            'class-ftp-sockets.php'                                 => "f",
            'import.php'                                            => "f",
            'class-wp-ms-themes-list-table.php'                     => "f",
            'class-wp-privacy-policy-content.php'                   => "f",
            'class-wp-automatic-updater.php'                        => "f",
            'update.php'                                            => "f",
            'nav-menu.php'                                          => "f",
            'class-wp-site-health.php'                              => "f",
            'class-theme-upgrader.php'                              => "f",
            'upgrade.php'                                           => "f",
            'class-wp-site-icon.php'                                => "f",
            'class-wp-debug-data.php'                               => "f",
            'class-wp-internal-pointers.php'                        => "f",
            'misc.php'                                              => "f",
            'schema.php'                                            => "f",
            'class-plugin-upgrader-skin.php'                        => "f",
            'ajax-actions.php'                                      => "f",
            'template.php'                                          => "f",
            'class-walker-nav-menu-checklist.php'                   => "f",
            'class-wp-plugins-list-table.php'                       => "f",
            'ms-admin-filters.php'                                  => "f",
            'class-ftp-pure.php'                                    => "f",
            'class-wp-theme-install-list-table.php'                 => "f",
            'class-wp-list-table-compat.php'                        => "f",
            'class-wp-filesystem-ftpsockets.php'                    => "f",
            'class-wp-privacy-data-export-requests-list-table.php'  => "f",
            'translation-install.php'                               => "f",
            'plugin.php'                                            => "f",
            'class-bulk-upgrader-skin.php'                          => "f",
            'class-wp-plugin-install-list-table.php'                => "f",
            'theme.php'                                             => "f",
            'class-pclzip.php'                                      => "f",
            'class-wp-users-list-table.php'                         => "f",
            'class-ftp.php'                                         => "f",
            'comment.php'                                           => "f",
            'class-wp-upgrader.php'                                 => "f",
            'class-wp-ms-sites-list-table.php'                      => "f",
            'class-wp-posts-list-table.php'                         => "f",
            'screen.php'                                            => "f",
            'class-wp-filesystem-ssh2.php'                          => "f",
            'media.php'                                             => "f",
            'class-wp-filesystem-ftpext.php'                        => "f",
            'export.php'                                            => "f",
            'class-wp-links-list-table.php'                         => "f",
            'class-plugin-upgrader.php'                             => "f",
            'deprecated.php'                                        => "f",
            'class-wp-upgrader-skin.php'                            => "f",
            'class-wp-comments-list-table.php'                      => "f",
            'class-wp-filesystem-base.php'                          => "f",
            'class-bulk-plugin-upgrader-skin.php'                   => "f",
            'file.php'                                              => "f",
            'widgets.php'                                           => "f",
            'class-wp-post-comments-list-table.php'                 => "f",
            'class-wp-terms-list-table.php'                         => "f",
            'class-wp-list-table.php'                               => "f",
        ),
        'ms-users.php'              => "f",
        'link-manager.php'          => "f",
        'freedoms.php'              => "f",
        'options-reading.php'       => "f",
        'comment.php'               => "f",
        'plugin-editor.php'         => "f",
        'nav-menus.php'             => "f",
        'media.php'                 => "f",
        'install.php'               => "f",
        'options-head.php'          => "f",
        'link-parse-opml.php'       => "f",
        'load-scripts.php'          => "f",
        'user-new.php'              => "f",
        'link-add.php'              => "f",
        'export.php'                => "f",
        'edit-comments.php'         => "f",
        'maint'                     => array('repair.php' => "f"),
        'install-helper.php'        => "f",
        'widgets-form-blocks.php'   => "f",
        'load-styles.php'           => "f",
        'js'                        => array(
            'farbtastic.js'                  => "f",
            'inline-edit-tax.min.js'         => "f",
            'press-this.js'                  => "f",
            'tags.min.js'                    => "f",
            'nav-menu.js'                    => "f",
            'media-gallery.min.js'           => "f",
            'bookmarklet.js'                 => "f",
            'editor-expand.min.js'           => "f",
            'svg-painter.min.js'             => "f",
            'media.min.js'                   => "f",
            'theme-plugin-editor.js'         => "f",
            'editor.js'                      => "f",
            'application-passwords.js'       => "f",
            'theme.js'                       => "f",
            'custom-background.js'           => "f",
            'media.js'                       => "f",
            'theme.min.js'                   => "f",
            'tags-box.js'                    => "f",
            'auth-app.min.js'                => "f",
            'edit-comments.min.js'           => "f",
            'code-editor.min.js'             => "f",
            'edit-comments.js'               => "f",
            'site-health.js'                 => "f",
            'language-chooser.min.js'        => "f",
            'dashboard.js'                   => "f",
            'theme-plugin-editor.min.js'     => "f",
            'wp-fullscreen-stub.js'          => "f",
            'site-icon.min.js'               => "f",
            'media-upload.js'                => "f",
            'post.min.js'                    => "f",
            'language-chooser.js'            => "f",
            'customize-widgets.js'           => "f",
            'wp-fullscreen.min.js'           => "f",
            'accordion.js'                   => "f",
            'code-editor.js'                 => "f",
            'set-post-thumbnail.min.js'      => "f",
            'updates.js'                     => "f",
            'custom-header.js'               => "f",
            'revisions.min.js'               => "f",
            'image-edit.js'                  => "f",
            'user-suggest.min.js'            => "f",
            'iris.min.js'                    => "f",
            'comment.min.js'                 => "f",
            'accordion.min.js'               => "f",
            'password-toggle.js'             => "f",
            'editor.min.js'                  => "f",
            'common.js'                      => "f",
            'privacy-tools.js'               => "f",
            'auth-app.js'                    => "f",
            'color-picker.js'                => "f",
            'nav-menu.min.js'                => "f",
            'media-upload.min.js'            => "f",
            'wp-fullscreen.js'               => "f",
            'word-count.js'                  => "f",
            'revisions.js'                   => "f",
            'site-icon.js'                   => "f",
            'password-strength-meter.js'     => "f",
            'password-toggle.min.js'         => "f",
            'user-profile.min.js'            => "f",
            'comment.js'                     => "f",
            'site-health.min.js'             => "f",
            'inline-edit-tax.js'             => "f",
            'plugin-install.js'              => "f",
            'wp-fullscreen-stub.min.js'      => "f",
            'user-suggest.js'                => "f",
            'tags.js'                        => "f",
            'common.min.js'                  => "f",
            'svg-painter.js'                 => "f",
            'password-strength-meter.min.js' => "f",
            'press-this.min.js'              => "f",
            'inline-edit-post.js'            => "f",
            'widgets.js'                     => "f",
            'xfn.min.js'                     => "f",
            'editor-expand.js'               => "f",
            'gallery.js'                     => "f",
            'customize-controls.js'          => "f",
            'tags-suggest.min.js'            => "f",
            'link.js'                        => "f",
            'link.min.js'                    => "f",
            'customize-controls.min.js'      => "f",
            'customize-widgets.min.js'       => "f",
            'bookmarklet.min.js'             => "f",
            'word-count.min.js'              => "f",
            'xfn.js'                         => "f",
            'customize-nav-menus.js'         => "f",
            'privacy-tools.min.js'           => "f",
            'customize-nav-menus.min.js'     => "f",
            'updates.min.js'                 => "f",
            'set-post-thumbnail.js'          => "f",
            'post.js'                        => "f",
            'media-gallery.js'               => "f",
            'widgets'                        => array(
                'media-audio-widget.js'       => "f",
                'media-video-widget.min.js'   => "f",
                'media-gallery-widget.js'     => "f",
                'media-audio-widget.min.js'   => "f",
                'media-video-widget.js'       => "f",
                'media-widgets.min.js'        => "f",
                'media-widgets.js'            => "f",
                'media-image-widget.min.js'   => "f",
                'media-image-widget.js'       => "f",
                'text-widgets.js'             => "f",
                'custom-html-widgets.js'      => "f",
                'text-widgets.min.js'         => "f",
                'media-gallery-widget.min.js' => "f",
                'custom-html-widgets.min.js'  => "f",
            ),
            'tags-suggest.js'                => "f",
            'postbox.min.js'                 => "f",
            'color-picker.min.js'            => "f",
            'gallery.min.js'                 => "f",
            'dashboard.min.js'               => "f",
            'widgets.min.js'                 => "f",
            'postbox.js'                     => "f",
            'application-passwords.min.js'   => "f",
            'custom-background.min.js'       => "f",
            'user-profile.js'                => "f",
            'inline-edit-post.min.js'        => "f",
            'tags-box.min.js'                => "f",
            'plugin-install.min.js'          => "f",
            'image-edit.min.js'              => "f",
        ),
        'admin-header.php'          => "f",
        'options-general.php'       => "f",
        'widgets.php'               => "f",
        'edit-tag-form.php'         => "f",
        'profile.php'               => "f",
        'ms-admin.php'              => "f",
        'images'                    => array(
            'icons32-2x.png'               => "f",
            'post-formats-vs.png'          => "f",
            'about-color-palette.svg'      => "f",
            'privacy.svg'                  => "f",
            'contribute-no-code.svg'       => "f",
            'post-formats32-vs.png'        => "f",
            'align-right.png'              => "f",
            'align-center.png'             => "f",
            'about-header-brushes.svg'     => "f",
            'post-formats.png'             => "f",
            'menu-2x.png'                  => "f",
            'freedom-2.svg'                => "f",
            'about-color-palette-vert.svg' => "f",
            'icons32.png'                  => "f",
            'w-logo-white.png'             => "f",
            'post-formats32.png'           => "f",
            'media-button-other.gif'       => "f",
            'wordpress-logo.png'           => "f",
            'about-release-badge.svg'      => "f",
            'arrows.png'                   => "f",
            'contribute-code.svg'          => "f",
            'align-left.png'               => "f",
            'resize-2x.gif'                => "f",
            'browser-rtl.png'              => "f",
            'arrows-2x.png'                => "f",
            'about-header-background.svg'  => "f",
            'about-badge.svg'              => "f",
            'menu-vs-2x.png'               => "f",
            'media-button.png'             => "f",
            'resize.gif'                   => "f",
            'about-header-credits.svg'     => "f",
            'align-left-2x.png'            => "f",
            'marker.png'                   => "f",
            'loading.gif'                  => "f",
            'list-2x.png'                  => "f",
            'sort.gif'                     => "f",
            'bubble_bg-2x.gif'             => "f",
            'wpspin_light-2x.gif'          => "f",
            'se.png'                       => "f",
            'about-header-privacy.svg'     => "f",
            'yes.png'                      => "f",
            'privacy.png'                  => "f",
            'icons32-vs-2x.png'            => "f",
            'sort-2x.gif'                  => "f",
            'imgedit-icons-2x.png'         => "f",
            'freedom-3.svg'                => "f",
            'comment-grey-bubble-2x.png'   => "f",
            'comment-grey-bubble.png'      => "f",
            'no.png'                       => "f",
            'imgedit-icons.png'            => "f",
            'wpspin_light.gif'             => "f",
            'wheel.png'                    => "f",
            'align-none-2x.png'            => "f",
            'dashboard-background.svg'     => "f",
            'media-button-music.gif'       => "f",
            'browser.png'                  => "f",
            'about-header-contribute.svg'  => "f",
            'stars.png'                    => "f",
            'contribute-main.svg'          => "f",
            'icons32-vs.png'               => "f",
            'mask.png'                     => "f",
            'xit.gif'                      => "f",
            'generic.png'                  => "f",
            'align-none.png'               => "f",
            'spinner.gif'                  => "f",
            'wordpress-logo.svg'           => "f",
            'menu-vs.png'                  => "f",
            'freedom-4.svg'                => "f",
            'date-button-2x.gif'           => "f",
            'resize-rtl-2x.gif'            => "f",
            'about-header-about.svg'       => "f",
            'media-button-2x.png'          => "f",
            'spinner-2x.gif'               => "f",
            'bubble_bg.gif'                => "f",
            'wordpress-logo-white.svg'     => "f",
            'about-header-freedoms.svg'    => "f",
            'align-right-2x.png'           => "f",
            'list.png'                     => "f",
            'menu.png'                     => "f",
            'media-button-image.gif'       => "f",
            'stars-2x.png'                 => "f",
            'align-center-2x.png'          => "f",
            'freedoms.png'                 => "f",
            'resize-rtl.gif'               => "f",
            'date-button.gif'              => "f",
            'about-texture.png'            => "f",
            'media-button-video.gif'       => "f",
            'xit-2x.gif'                   => "f",
            'w-logo-blue.png'              => "f",
            'freedom-1.svg'                => "f",
        ),
        'themes.php'                => "f",
        'post-new.php'              => "f",
    ),
    'wp-trackback.php'     => "f",
    'xmlrpc.php'           => "f",
    'wp-links-opml.php'    => "f",
    'wp-includes'          => array(
        'navigation-fallback.php'                        => "f",
        'class-wp-theme-json.php'                        => "f",
        'class-wp-recovery-mode-email-service.php'       => "f",
        'ms-functions.php'                               => "f",
        'class-wp-feed-cache-transient.php'              => "f",
        'class-wp-hook.php'                              => "f",
        'class-wp-matchesmapregex.php'                   => "f",
        'embed-template.php'                             => "f",
        'ms-default-filters.php'                         => "f",
        'feed.php'                                       => "f",
        'class-simplepie.php'                            => "f",
        'class-wp-admin-bar.php'                         => "f",
        'global-styles-and-settings.php'                 => "f",
        'class-wp-metadata-lazyloader.php'               => "f",
        'ID3'                                            => array(
            'module.audio-video.flv.php'       => "f",
            'module.audio.dts.php'             => "f",
            'module.tag.apetag.php'            => "f",
            'getid3.lib.php'                   => "f",
            'module.tag.id3v2.php'             => "f",
            'license.commercial.txt'           => "f",
            'module.audio.ac3.php'             => "f",
            'module.audio.mp3.php'             => "f",
            'module.audio-video.asf.php'       => "f",
            'getid3.php'                       => "f",
            'module.audio-video.matroska.php'  => "f",
            'module.audio-video.quicktime.php' => "f",
            'module.audio-video.riff.php'      => "f",
            'module.audio.flac.php'            => "f",
            'readme.txt'                       => "f",
            'module.tag.lyrics3.php'           => "f",
            'module.tag.id3v1.php'             => "f",
            'license.txt'                      => "f",
            'module.audio.ogg.php'             => "f",
        ),
        'class-wp-http-curl.php'                         => "f",
        'class-wp-network-query.php'                     => "f",
        'block-editor.php'                               => "f",
        'class-wp-role.php'                              => "f",
        'class-wp-date-query.php'                        => "f",
        'class-wp-customize-widgets.php'                 => "f",
        'SimplePie'                                      => array(
            'Restriction.php' => "f",
            'library'         => array(
                'SimplePie'     => array(
                    'Restriction.php' => "f",
                    'Category.php'    => "f",
                    'IRI.php'         => "f",
                    'Caption.php'     => "f",
                    'Net'             => array('IPv6.php' => "f"),
                    'Cache.php'       => "f",
                    'Parser.php'      => "f",
                    'HTTP'            => array('Parser.php' => "f"),
                    'Content'         => array(
                        'Type' => array('Sniffer.php' => "f"),
                    ),
                    'Item.php'        => "f",
                    'Exception.php'   => "f",
                    'Registry.php'    => "f",
                    'Misc.php'        => "f",
                    'Cache'           => array(
                        'Memcache.php'  => "f",
                        'DB.php'        => "f",
                        'File.php'      => "f",
                        'MySQL.php'     => "f",
                        'Base.php'      => "f",
                        'Redis.php'     => "f",
                        'Memcached.php' => "f",
                    ),
                    'Core.php'        => "f",
                    'File.php'        => "f",
                    'Rating.php'      => "f",
                    'Decode'          => array(
                        'HTML' => array('Entities.php' => "f"),
                    ),
                    'gzdecode.php'    => "f",
                    'Author.php'      => "f",
                    'Copyright.php'   => "f",
                    'XML'             => array(
                        'Declaration' => array('Parser.php' => "f"),
                    ),
                    'Locator.php'     => "f",
                    'Parse'           => array('Date.php' => "f"),
                    'Credit.php'      => "f",
                    'Source.php'      => "f",
                    'Sanitize.php'    => "f",
                    'Enclosure.php'   => "f",
                ),
                'SimplePie.php' => "f",
            ),
            'Category.php'    => "f",
            'IRI.php'         => "f",
            'Caption.php'     => "f",
            'Net'             => array('IPv6.php' => "f"),
            'Cache.php'       => "f",
            'Parser.php'      => "f",
            'HTTP'            => array('Parser.php' => "f"),
            'Content'         => array(
                'Type' => array('Sniffer.php' => "f"),
            ),
            'Item.php'        => "f",
            'Exception.php'   => "f",
            'Registry.php'    => "f",
            'Misc.php'        => "f",
            'Cache'           => array(
                'Memcache.php'  => "f",
                'DB.php'        => "f",
                'File.php'      => "f",
                'MySQL.php'     => "f",
                'Base.php'      => "f",
                'Redis.php'     => "f",
                'Memcached.php' => "f",
            ),
            'Core.php'        => "f",
            'File.php'        => "f",
            'src'             => array(
                'Restriction.php'   => "f",
                'Category.php'      => "f",
                'IRI.php'           => "f",
                'Caption.php'       => "f",
                'Net'               => array('IPv6.php' => "f"),
                'Cache.php'         => "f",
                'Parser.php'        => "f",
                'HTTP'              => array('Parser.php' => "f"),
                'SimplePie.php'     => "f",
                'Content'           => array(
                    'Type' => array('Sniffer.php' => "f"),
                ),
                'Item.php'          => "f",
                'Exception.php'     => "f",
                'Registry.php'      => "f",
                'Misc.php'          => "f",
                'Cache'             => array(
                    'Memcache.php'           => "f",
                    'Psr16.php'              => "f",
                    'NameFilter.php'         => "f",
                    'DB.php'                 => "f",
                    'File.php'               => "f",
                    'MySQL.php'              => "f",
                    'BaseDataCache.php'      => "f",
                    'CallableNameFilter.php' => "f",
                    'Base.php'               => "f",
                    'DataCache.php'          => "f",
                    'Redis.php'              => "f",
                    'Memcached.php'          => "f",
                ),
                'Core.php'          => "f",
                'File.php'          => "f",
                'Rating.php'        => "f",
                'Decode'            => array(
                    'HTML' => array('Entities.php' => "f"),
                ),
                'RegistryAware.php' => "f",
                'Author.php'        => "f",
                'Copyright.php'     => "f",
                'XML'               => array(
                    'Declaration' => array('Parser.php' => "f"),
                ),
                'Locator.php'       => "f",
                'Parse'             => array('Date.php' => "f"),
                'Credit.php'        => "f",
                'Source.php'        => "f",
                'Sanitize.php'      => "f",
                'Enclosure.php'     => "f",
                'Gzdecode.php'      => "f",
            ),
            'Rating.php'      => "f",
            'Decode'          => array(
                'HTML' => array('Entities.php' => "f"),
            ),
            'gzdecode.php'    => "f",
            'Author.php'      => "f",
            'Copyright.php'   => "f",
            'autoloader.php'  => "f",
            'XML'             => array(
                'Declaration' => array('Parser.php' => "f"),
            ),
            'Locator.php'     => "f",
            'Parse'           => array('Date.php' => "f"),
            'Credit.php'      => "f",
            'Source.php'      => "f",
            'Sanitize.php'    => "f",
            'Enclosure.php'   => "f",
        ),
        'class-wp-recovery-mode.php'                     => "f",
        'class-wp-customize-manager.php'                 => "f",
        'canonical.php'                                  => "f",
        'customize'                                      => array(
            'class-wp-customize-nav-menu-item-setting.php'       => "f",
            'class-wp-customize-sidebar-section.php'             => "f",
            'class-wp-customize-nav-menus-panel.php'             => "f",
            'class-wp-customize-cropped-image-control.php'       => "f",
            'class-wp-customize-filter-setting.php'              => "f",
            'class-wp-customize-date-time-control.php'           => "f",
            'class-wp-customize-nav-menu-setting.php'            => "f",
            'class-wp-customize-upload-control.php'              => "f",
            'class-wp-customize-site-icon-control.php'           => "f",
            'class-wp-customize-partial.php'                     => "f",
            'class-wp-customize-themes-section.php'              => "f",
            'class-wp-customize-selective-refresh.php'           => "f",
            'class-wp-customize-header-image-setting.php'        => "f",
            'class-wp-customize-nav-menu-name-control.php'       => "f",
            'class-wp-widget-form-customize-control.php'         => "f",
            'class-wp-widget-area-customize-control.php'         => "f",
            'class-wp-customize-custom-css-setting.php'          => "f",
            'class-wp-customize-nav-menu-item-control.php'       => "f",
            'class-wp-customize-background-image-setting.php'    => "f",
            'class-wp-sidebar-block-editor-control.php'          => "f",
            'class-wp-customize-nav-menu-auto-add-control.php'   => "f",
            'class-wp-customize-themes-panel.php'                => "f",
            'class-wp-customize-new-menu-section.php'            => "f",
            'class-wp-customize-code-editor-control.php'         => "f",
            'class-wp-customize-nav-menu-section.php'            => "f",
            'class-wp-customize-nav-menu-control.php'            => "f",
            'class-wp-customize-background-image-control.php'    => "f",
            'class-wp-customize-new-menu-control.php'            => "f",
            'class-wp-customize-background-position-control.php' => "f",
            'class-wp-customize-theme-control.php'               => "f",
            'class-wp-customize-nav-menu-locations-control.php'  => "f",
            'class-wp-customize-image-control.php'               => "f",
            'class-wp-customize-header-image-control.php'        => "f",
            'class-wp-customize-media-control.php'               => "f",
            'class-wp-customize-color-control.php'               => "f",
            'class-wp-customize-nav-menu-location-control.php'   => "f",
        ),
        'class-wp-http-ixr-client.php'                   => "f",
        'revision.php'                                   => "f",
        'class-wp-theme-json-data.php'                   => "f",
        'class-oembed.php'                               => "f",
        'ms-network.php'                                 => "f",
        'class-wp-block-type-registry.php'               => "f",
        'session.php'                                    => "f",
        'rss.php'                                        => "f",
        'default-constants.php'                          => "f",
        'class-wp-simplepie-sanitize-kses.php'           => "f",
        'IXR'                                            => array(
            'class-IXR-value.php'               => "f",
            'class-IXR-message.php'             => "f",
            'class-IXR-error.php'               => "f",
            'class-IXR-request.php'             => "f",
            'class-IXR-server.php'              => "f",
            'class-IXR-base64.php'              => "f",
            'class-IXR-introspectionserver.php' => "f",
            'class-IXR-clientmulticall.php'     => "f",
            'class-IXR-date.php'                => "f",
            'class-IXR-client.php'              => "f",
        ),
        'class-wp-block-supports.php'                    => "f",
        'class-wp-block-patterns-registry.php'           => "f",
        'class-wp-session-tokens.php'                    => "f",
        'wp-db.php'                                      => "f",
        'wp-diff.php'                                    => "f",
        'class-wp-block-parser-frame.php'                => "f",
        'class-wp-theme-json-resolver.php'               => "f",
        'class-wp-block-editor-context.php'              => "f",
        'class-wp-locale.php'                            => "f",
        'class-wp-classic-to-block-menu-converter.php'   => "f",
        'class-walker-page.php'                          => "f",
        'bookmark.php'                                   => "f",
        'class-wp-locale-switcher.php'                   => "f",
        'ms-deprecated.php'                              => "f",
        'media-template.php'                             => "f",
        'rest-api'                                       => array(
            'class-wp-rest-request.php'  => "f",
            'fields'                     => array(
                'class-wp-rest-user-meta-fields.php'    => "f",
                'class-wp-rest-post-meta-fields.php'    => "f",
                'class-wp-rest-meta-fields.php'         => "f",
                'class-wp-rest-term-meta-fields.php'    => "f",
                'class-wp-rest-comment-meta-fields.php' => "f",
            ),
            'endpoints'                  => array(
                'class-wp-rest-block-types-controller.php'              => "f",
                'class-wp-rest-revisions-controller.php'                => "f",
                'class-wp-rest-search-controller.php'                   => "f",
                'class-wp-rest-menus-controller.php'                    => "f",
                'class-wp-rest-block-pattern-categories-controller.php' => "f",
                'class-wp-rest-site-health-controller.php'              => "f",
                'class-wp-rest-themes-controller.php'                   => "f",
                'class-wp-rest-font-families-controller.php'            => "f",
                'class-wp-rest-widgets-controller.php'                  => "f",
                'class-wp-rest-font-faces-controller.php'               => "f",
                'class-wp-rest-edit-site-export-controller.php'         => "f",
                'class-wp-rest-autosaves-controller.php'                => "f",
                'class-wp-rest-block-directory-controller.php'          => "f",
                'class-wp-rest-font-collections-controller.php'         => "f",
                'class-wp-rest-template-autosaves-controller.php'       => "f",
                'class-wp-rest-navigation-fallback-controller.php'      => "f",
                'class-wp-rest-plugins-controller.php'                  => "f",
                'class-wp-rest-templates-controller.php'                => "f",
                'class-wp-rest-global-styles-controller.php'            => "f",
                'class-wp-rest-global-styles-revisions-controller.php'  => "f",
                'class-wp-rest-block-patterns-controller.php'           => "f",
                'class-wp-rest-sidebars-controller.php'                 => "f",
                'class-wp-rest-blocks-controller.php'                   => "f",
                'class-wp-rest-post-statuses-controller.php'            => "f",
                'class-wp-rest-pattern-directory-controller.php'        => "f",
                'class-wp-rest-attachments-controller.php'              => "f",
                'class-wp-rest-terms-controller.php'                    => "f",
                'class-wp-rest-block-renderer-controller.php'           => "f",
                'class-wp-rest-application-passwords-controller.php'    => "f",
                'class-wp-rest-controller.php'                          => "f",
                'class-wp-rest-posts-controller.php'                    => "f",
                'class-wp-rest-menu-locations-controller.php'           => "f",
                'class-wp-rest-settings-controller.php'                 => "f",
                'class-wp-rest-url-details-controller.php'              => "f",
                'class-wp-rest-widget-types-controller.php'             => "f",
                'class-wp-rest-template-revisions-controller.php'       => "f",
                'class-wp-rest-post-types-controller.php'               => "f",
                'class-wp-rest-comments-controller.php'                 => "f",
                'class-wp-rest-users-controller.php'                    => "f",
                'class-wp-rest-taxonomies-controller.php'               => "f",
                'class-wp-rest-menu-items-controller.php'               => "f",
            ),
            'search'                     => array(
                'class-wp-rest-term-search-handler.php'        => "f",
                'class-wp-rest-post-search-handler.php'        => "f",
                'class-wp-rest-search-handler.php'             => "f",
                'class-wp-rest-post-format-search-handler.php' => "f",
            ),
            'class-wp-rest-server.php'   => "f",
            'class-wp-rest-response.php' => "f",
        ),
        'class-wp-http.php'                              => "f",
        'template-canvas.php'                            => "f",
        'PHPMailer'                                      => array(
            'SMTP.php'      => "f",
            'Exception.php' => "f",
            'PHPMailer.php' => "f",
        ),
        'user.php'                                       => "f",
        'fonts.php'                                      => "f",
        'ms-settings.php'                                => "f",
        'option.php'                                     => "f",
        'class-wp-customize-control.php'                 => "f",
        'class-wp-user.php'                              => "f",
        'class-walker-category.php'                      => "f",
        'Text'                                           => array(
            'Exception.php' => "f",
            'Diff.php'      => "f",
            'Diff'          => array(
                'Renderer.php' => "f",
                'Renderer'     => array('inline.php' => "f"),
                'Engine'       => array(
                    'xdiff.php'  => "f",
                    'shell.php'  => "f",
                    'string.php' => "f",
                    'native.php' => "f",
                ),
            ),
        ),
        'category-template.php'                          => "f",
        'class-IXR.php'                                  => "f",
        'post-thumbnail-template.php'                    => "f",
        'class-wp-roles.php'                             => "f",
        'formatting.php'                                 => "f",
        'assets'                                         => array(
            'script-loader-react-refresh-runtime.php'     => "f",
            'script-modules-packages.min.php'             => "f",
            'script-loader-packages.php'                  => "f",
            'script-loader-react-refresh-entry.php'       => "f",
            'script-loader-packages.min.php'              => "f",
            'script-modules-packages.php'                 => "f",
            'script-loader-react-refresh-runtime.min.php' => "f",
            'script-loader-react-refresh-entry.min.php'   => "f",
        ),
        'random_compat'                                  => array(
            'random.php'                        => "f",
            'random_bytes_libsodium.php'        => "f",
            'random_bytes_mcrypt.php'           => "f",
            'byte_safe_strings.php'             => "f",
            'error_polyfill.php'                => "f",
            'random_bytes_com_dotnet.php'       => "f",
            'cast_to_int.php'                   => "f",
            'random_bytes_openssl.php'          => "f",
            'random_int.php'                    => "f",
            'random_bytes_libsodium_legacy.php' => "f",
            'random_bytes_dev_urandom.php'      => "f",
        ),
        'compat.php'                                     => "f",
        'class-wp-block-bindings-registry.php'           => "f",
        'class-wp-recovery-mode-link-service.php'        => "f",
        'functions.wp-styles.php'                        => "f",
        'post.php'                                       => "f",
        'class-wp-exception.php'                         => "f",
        'class-wp-customize-setting.php'                 => "f",
        'class-wp-block-templates-registry.php'          => "f",
        'class-http.php'                                 => "f",
        'class-wp-network.php'                           => "f",
        'class-wp-http-streams.php'                      => "f",
        'rss-functions.php'                              => "f",
        'ms-blogs.php'                                   => "f",
        'class-wp-rewrite.php'                           => "f",
        'class-wp-comment-query.php'                     => "f",
        'rest-api.php'                                   => "f",
        'class-phpass.php'                               => "f",
        'meta.php'                                       => "f",
        'author-template.php'                            => "f",
        'class-wp-term.php'                              => "f",
        'class-walker-category-dropdown.php'             => "f",
        'html-api'                                       => array(
            'class-wp-html-active-formatting-elements.php' => "f",
            'class-wp-html-token.php'                      => "f",
            'class-wp-html-stack-event.php'                => "f",
            'class-wp-html-processor-state.php'            => "f",
            'class-wp-html-processor.php'                  => "f",
            'class-wp-html-attribute-token.php'            => "f",
            'class-wp-html-open-elements.php'              => "f",
            'class-wp-html-doctype-info.php'               => "f",
            'class-wp-html-span.php'                       => "f",
            'html5-named-character-references.php'         => "f",
            'class-wp-html-unsupported-exception.php'      => "f",
            'class-wp-html-tag-processor.php'              => "f",
            'class-wp-html-decoder.php'                    => "f",
            'class-wp-html-text-replacement.php'           => "f",
        ),
        'kses.php'                                       => "f",
        'class-smtp.php'                                 => "f",
        'block-i18n.json'                                => "f",
        'class-wp-block-template.php'                    => "f",
        'class-wp-widget-factory.php'                    => "f",
        'script-modules.php'                             => "f",
        'error-protection.php'                           => "f",
        'class-wp-user-request.php'                      => "f",
        'class-wp-list-util.php'                         => "f",
        'nav-menu-template.php'                          => "f",
        'class-requests.php'                             => "f",
        'class-wp-http-requests-hooks.php'               => "f",
        'default-widgets.php'                            => "f",
        'theme.json'                                     => "f",
        'class-wp-feed-cache.php'                        => "f",
        'class.wp-dependencies.php'                      => "f",
        'class-wp-meta-query.php'                        => "f",
        'theme-previews.php'                             => "f",
        'vars.php'                                       => "f",
        'sitemaps.php'                                   => "f",
        'general-template.php'                           => "f",
        'class-avif-info.php'                            => "f",
        'taxonomy.php'                                   => "f",
        'registration.php'                               => "f",
        'class-wp-block-parser-block.php'                => "f",
        'class-wp-ajax-response.php'                     => "f",
        'class-wp-theme-json-schema.php'                 => "f",
        'class-wp-plugin-dependencies.php'               => "f",
        'class-walker-nav-menu.php'                      => "f",
        'class-wp-error.php'                             => "f",
        'class.wp-styles.php'                            => "f",
        'ms-load.php'                                    => "f",
        'comment-template.php'                           => "f",
        'php-compat'                                     => array('readonly.php' => "f"),
        'class-wp-tax-query.php'                         => "f",
        'class-wp-widget.php'                            => "f",
        'update.php'                                     => "f",
        'class-wp-customize-panel.php'                   => "f",
        'nav-menu.php'                                   => "f",
        'class-wp-block-pattern-categories-registry.php' => "f",
        'script-loader.php'                              => "f",
        'version.php'                                    => "f",
        'Requests'                                       => array(
            'library'         => array('Requests.php' => "f"),
            'IRI.php'         => "f",
            'Proxy'           => array('HTTP.php' => "f"),
            'IDNAEncoder.php' => "f",
            'Exception.php'   => "f",
            'Response'        => array('Headers.php' => "f"),
            'Hooks.php'       => "f",
            'Cookie'          => array('Jar.php' => "f"),
            'SSL.php'         => "f",
            'Proxy.php'       => "f",
            'Cookie.php'      => "f",
            'Session.php'     => "f",
            'Response.php'    => "f",
            'src'             => array(
                'Capability.php'  => "f",
                'Proxy'           => array('Http.php' => "f"),
                'Exception.php'   => "f",
                'Response'        => array('Headers.php' => "f"),
                'Hooks.php'       => "f",
                'Cookie'          => array('Jar.php' => "f"),
                'Proxy.php'       => "f",
                'Autoload.php'    => "f",
                'Cookie.php'      => "f",
                'Session.php'     => "f",
                'Response.php'    => "f",
                'Port.php'        => "f",
                'HookManager.php' => "f",
                'Ssl.php'         => "f",
                'Transport'       => array(
                    'Fsockopen.php' => "f",
                    'Curl.php'      => "f",
                ),
                'Utility'         => array(
                    'FilteredIterator.php'          => "f",
                    'InputValidator.php'            => "f",
                    'CaseInsensitiveDictionary.php' => "f",
                ),
                'Auth'            => array('Basic.php' => "f"),
                'Iri.php'         => "f",
                'IdnaEncoder.php' => "f",
                'Ipv6.php'        => "f",
                'Requests.php'    => "f",
                'Transport.php'   => "f",
                'Exception'       => array(
                    'InvalidArgument.php' => "f",
                    'Http'                => array(
                        'Status400.php'     => "f",
                        'Status428.php'     => "f",
                        'Status505.php'     => "f",
                        'Status418.php'     => "f",
                        'Status305.php'     => "f",
                        'Status414.php'     => "f",
                        'Status406.php'     => "f",
                        'Status304.php'     => "f",
                        'Status407.php'     => "f",
                        'Status503.php'     => "f",
                        'Status431.php'     => "f",
                        'Status404.php'     => "f",
                        'Status402.php'     => "f",
                        'Status511.php'     => "f",
                        'Status411.php'     => "f",
                        'Status413.php'     => "f",
                        'Status401.php'     => "f",
                        'Status500.php'     => "f",
                        'Status504.php'     => "f",
                        'Status403.php'     => "f",
                        'Status306.php'     => "f",
                        'Status410.php'     => "f",
                        'Status429.php'     => "f",
                        'Status408.php'     => "f",
                        'StatusUnknown.php' => "f",
                        'Status405.php'     => "f",
                        'Status415.php'     => "f",
                        'Status416.php'     => "f",
                        'Status502.php'     => "f",
                        'Status412.php'     => "f",
                        'Status417.php'     => "f",
                        'Status409.php'     => "f",
                        'Status501.php'     => "f",
                    ),
                    'Transport'           => array('Curl.php' => "f"),
                    'ArgumentCount.php'   => "f",
                    'Transport.php'       => "f",
                    'Http.php'            => "f",
                ),
                'Auth.php'        => "f",
            ),
            'IPv6.php'        => "f",
            'Transport'       => array(
                'fsockopen.php' => "f",
                'cURL.php'      => "f",
            ),
            'Utility'         => array(
                'FilteredIterator.php'          => "f",
                'CaseInsensitiveDictionary.php' => "f",
            ),
            'Auth'            => array('Basic.php' => "f"),
            'Transport.php'   => "f",
            'Exception'       => array(
                'HTTP'          => array(
                    '404.php'     => "f",
                    '305.php'     => "f",
                    '416.php'     => "f",
                    '409.php'     => "f",
                    '431.php'     => "f",
                    '429.php'     => "f",
                    '406.php'     => "f",
                    '500.php'     => "f",
                    '403.php'     => "f",
                    '408.php'     => "f",
                    'Unknown.php' => "f",
                    '412.php'     => "f",
                    '304.php'     => "f",
                    '401.php'     => "f",
                    '511.php'     => "f",
                    '407.php'     => "f",
                    '415.php'     => "f",
                    '504.php'     => "f",
                    '400.php'     => "f",
                    '428.php'     => "f",
                    '501.php'     => "f",
                    '410.php'     => "f",
                    '417.php'     => "f",
                    '503.php'     => "f",
                    '306.php'     => "f",
                    '411.php'     => "f",
                    '405.php'     => "f",
                    '502.php'     => "f",
                    '413.php'     => "f",
                    '505.php'     => "f",
                    '418.php'     => "f",
                    '414.php'     => "f",
                    '402.php'     => "f",
                ),
                'HTTP.php'      => "f",
                'Transport'     => array('cURL.php' => "f"),
                'Transport.php' => "f",
            ),
            'Hooker.php'      => "f",
            'Auth.php'        => "f",
        ),
        'class-wp-user-query.php'                        => "f",
        'class-snoopy.php'                               => "f",
        'class-wp-fatal-error-handler.php'               => "f",
        'class-wp-theme.php'                             => "f",
        'class-wp-post-type.php'                         => "f",
        'interactivity-api'                              => array(
            'class-wp-interactivity-api.php'                      => "f",
            'class-wp-interactivity-api-directives-processor.php' => "f",
            'interactivity-api.php'                               => "f",
        ),
        'class-wp-embed.php'                             => "f",
        'pomo'                                           => array(
            'plural-forms.php' => "f",
            'po.php'           => "f",
            'streams.php'      => "f",
            'translations.php' => "f",
            'mo.php'           => "f",
            'entry.php'        => "f",
        ),
        'style-engine.php'                               => "f",
        'css'                                            => array(
            'admin-bar.css'                 => "f",
            'wp-pointer-rtl.css'            => "f",
            'wp-pointer-rtl.min.css'        => "f",
            'customize-preview-rtl.css'     => "f",
            'editor.min.css'                => "f",
            'wp-embed-template.css'         => "f",
            'media-views.css'               => "f",
            'customize-preview.min.css'     => "f",
            'buttons.css'                   => "f",
            'wp-auth-check-rtl.min.css'     => "f",
            'dashicons.css'                 => "f",
            'admin-bar-rtl.css'             => "f",
            'customize-preview-rtl.min.css' => "f",
            'media-views-rtl.min.css'       => "f",
            'dist'                          => array(
                'edit-post'            => array(
                    'style.css'           => "f",
                    'classic.css'         => "f",
                    'style-rtl.css'       => "f",
                    'classic-rtl.min.css' => "f",
                    'classic.min.css'     => "f",
                    'style-rtl.min.css'   => "f",
                    'style.min.css'       => "f",
                    'classic-rtl.css'     => "f",
                ),
                'block-library'        => array(
                    'style.css'                   => "f",
                    'common-rtl.min.css'          => "f",
                    'editor.min.css'              => "f",
                    'classic.css'                 => "f",
                    'style-rtl.css'               => "f",
                    'classic-rtl.min.css'         => "f",
                    'common.min.css'              => "f",
                    'elements-rtl.css'            => "f",
                    'theme-rtl.css'               => "f",
                    'theme.min.css'               => "f",
                    'elements.css'                => "f",
                    'reset.min.css'               => "f",
                    'editor-elements-rtl.css'     => "f",
                    'classic.min.css'             => "f",
                    'editor-rtl.min.css'          => "f",
                    'editor.css'                  => "f",
                    'reset.css'                   => "f",
                    'theme-rtl.min.css'           => "f",
                    'common.css'                  => "f",
                    'reset-rtl.css'               => "f",
                    'editor-rtl.css'              => "f",
                    'elements-rtl.min.css'        => "f",
                    'reset-rtl.min.css'           => "f",
                    'common-rtl.css'              => "f",
                    'elements.min.css'            => "f",
                    'editor-elements.css'         => "f",
                    'editor-elements.min.css'     => "f",
                    'style-rtl.min.css'           => "f",
                    'style.min.css'               => "f",
                    'classic-rtl.css'             => "f",
                    'editor-elements-rtl.min.css' => "f",
                    'theme.css'                   => "f",
                ),
                'edit-widgets'         => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'nux'                  => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'components'           => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'preferences'          => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'commands'             => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'edit-site'            => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'posts-rtl.min.css' => "f",
                    'posts.min.css'     => "f",
                    'posts-rtl.css'     => "f",
                    'posts.css'         => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'reusable-blocks'      => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'block-editor'         => array(
                    'style.css'                         => "f",
                    'style-rtl.css'                     => "f",
                    'default-editor-styles-rtl.css'     => "f",
                    'default-editor-styles.css'         => "f",
                    'content.min.css'                   => "f",
                    'content.css'                       => "f",
                    'content-rtl.css'                   => "f",
                    'content-rtl.min.css'               => "f",
                    'default-editor-styles.min.css'     => "f",
                    'style-rtl.min.css'                 => "f",
                    'style.min.css'                     => "f",
                    'default-editor-styles-rtl.min.css' => "f",
                ),
                'block-directory'      => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'patterns'             => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'customize-widgets'    => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'editor'               => array(
                    'style.css'                 => "f",
                    'style-rtl.css'             => "f",
                    'editor-styles-rtl.css'     => "f",
                    'editor-styles-rtl.min.css' => "f",
                    'editor-styles.css'         => "f",
                    'editor-styles.min.css'     => "f",
                    'style-rtl.min.css'         => "f",
                    'style.min.css'             => "f",
                ),
                'widgets'              => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'list-reusable-blocks' => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
                'format-library'       => array(
                    'style.css'         => "f",
                    'style-rtl.css'     => "f",
                    'style-rtl.min.css' => "f",
                    'style.min.css'     => "f",
                ),
            ),
            'classic-themes.min.css'        => "f",
            'customize-preview.css'         => "f",
            'buttons.min.css'               => "f",
            'classic-themes.css'            => "f",
            'wp-embed-template-ie.min.css'  => "f",
            'jquery-ui-dialog-rtl.css'      => "f",
            'admin-bar.min.css'             => "f",
            'media-views.min.css'           => "f",
            'buttons-rtl.css'               => "f",
            'jquery-ui-dialog-rtl.min.css'  => "f",
            'editor-rtl.min.css'            => "f",
            'editor.css'                    => "f",
            'buttons-rtl.min.css'           => "f",
            'wp-auth-check.css'             => "f",
            'wp-pointer.css'                => "f",
            'editor-rtl.css'                => "f",
            'admin-bar-rtl.min.css'         => "f",
            'jquery-ui-dialog.min.css'      => "f",
            'media-views-rtl.css'           => "f",
            'jquery-ui-dialog.css'          => "f",
            'wp-auth-check.min.css'         => "f",
            'wp-embed-template.min.css'     => "f",
            'dashicons.min.css'             => "f",
            'wp-pointer.min.css'            => "f",
            'wp-embed-template-ie.css'      => "f",
            'wp-auth-check-rtl.css'         => "f",
        ),
        'class-wp-image-editor-gd.php'                   => "f",
        'l10n'                                           => array(
            'class-wp-translation-file-mo.php'    => "f",
            'class-wp-translation-file.php'       => "f",
            'class-wp-translation-file-php.php'   => "f",
            'class-wp-translations.php'           => "f",
            'class-wp-translation-controller.php' => "f",
        ),
        'template-loader.php'                            => "f",
        'class-pop3.php'                                 => "f",
        'block-patterns.php'                             => "f",
        'feed-atom-comments.php'                         => "f",
        'class-wp-site-query.php'                        => "f",
        'class-wp-simplepie-file.php'                    => "f",
        'block-bindings'                                 => array(
            'pattern-overrides.php' => "f",
            'post-meta.php'         => "f",
        ),
        'class-wp-recovery-mode-key-service.php'         => "f",
        'feed-rss2.php'                                  => "f",
        'class-wp-textdomain-registry.php'               => "f",
        'class-wpdb.php'                                 => "f",
        'class-wp.php'                                   => "f",
        'class-wp-recovery-mode-cookie-service.php'      => "f",
        'block-template.php'                             => "f",
        'blocks.php'                                     => "f",
        'class-wp-block-parser.php'                      => "f",
        'wlwmanifest.xml'                                => "f",
        'template.php'                                   => "f",
        'default-filters.php'                            => "f",
        'feed-rss2-comments.php'                         => "f",
        'class-wp-site.php'                              => "f",
        'class-wp-block-styles-registry.php'             => "f",
        'atomlib.php'                                    => "f",
        'class-wp-image-editor-imagick.php'              => "f",
        'registration-functions.php'                     => "f",
        'class-wp-styles.php'                            => "f",
        'class-wp-comment.php'                           => "f",
        'class-wp-block-bindings-source.php'             => "f",
        'class-wp-dependency.php'                        => "f",
        'feed-atom.php'                                  => "f",
        'class-wp-block.php'                             => "f",
        'class-wp-taxonomy.php'                          => "f",
        'locale.php'                                     => "f",
        'query.php'                                      => "f",
        'plugin.php'                                     => "f",
        'fonts'                                          => array(
            'dashicons.eot'                   => "f",
            'class-wp-font-face.php'          => "f",
            'class-wp-font-face-resolver.php' => "f",
            'class-wp-font-collection.php'    => "f",
            'dashicons.woff2'                 => "f",
            'dashicons.woff'                  => "f",
            'dashicons.ttf'                   => "f",
            'class-wp-font-library.php'       => "f",
            'class-wp-font-utils.php'         => "f",
            'dashicons.svg'                   => "f",
        ),
        'certificates'                                   => array('ca-bundle.crt' => "f"),
        'feed-rss.php'                                   => "f",
        'theme.php'                                      => "f",
        'class-wp-duotone.php'                           => "f",
        'class-wp-query.php'                             => "f",
        'class-phpmailer.php'                            => "f",
        'ms-files.php'                                   => "f",
        'pluggable.php'                                  => "f",
        'class-feed.php'                                 => "f",
        'class-wp-paused-extensions-storage.php'         => "f",
        'class-wp-http-response.php'                     => "f",
        'http.php'                                       => "f",
        'sitemaps'                                       => array(
            'providers'                        => array(
                'class-wp-sitemaps-taxonomies.php' => "f",
                'class-wp-sitemaps-posts.php'      => "f",
                'class-wp-sitemaps-users.php'      => "f",
            ),
            'class-wp-sitemaps-provider.php'   => "f",
            'class-wp-sitemaps-renderer.php'   => "f",
            'class-wp-sitemaps-stylesheet.php' => "f",
            'class-wp-sitemaps-registry.php'   => "f",
            'class-wp-sitemaps.php'            => "f",
            'class-wp-sitemaps-index.php'      => "f",
        ),
        'pluggable-deprecated.php'                       => "f",
        'https-migration.php'                            => "f",
        'class.wp-scripts.php'                           => "f",
        'date.php'                                       => "f",
        'theme-i18n.json'                                => "f",
        'class-walker-comment.php'                       => "f",
        'ms-default-constants.php'                       => "f",
        'comment.php'                                    => "f",
        'class-wp-http-proxy.php'                        => "f",
        'class-wp-walker.php'                            => "f",
        'post-formats.php'                               => "f",
        'class-wp-navigation-fallback.php'               => "f",
        'functions.php'                                  => "f",
        'class-wp-http-cookie.php'                       => "f",
        'post-template.php'                              => "f",
        'block-bindings.php'                             => "f",
        'class-wp-term-query.php'                        => "f",
        'class-wp-post.php'                              => "f",
        'class-json.php'                                 => "f",
        'class-wp-text-diff-renderer-table.php'          => "f",
        'shortcodes.php'                                 => "f",
        'admin-bar.php'                                  => "f",
        'class-wp-xmlrpc-server.php'                     => "f",
        'class-wp-customize-section.php'                 => "f",
        'feed-rdf.php'                                   => "f",
        'media.php'                                      => "f",
        'sodium_compat'                                  => array(
            'autoload-php7.php' => "f",
            'autoload.php'      => "f",
            'namespaced'        => array(
                'Core'       => array(
                    'Xsalsa20.php'   => "f",
                    'XChaCha20.php'  => "f",
                    'SipHash.php'    => "f",
                    'Util.php'       => "f",
                    'X25519.php'     => "f",
                    'ChaCha20'       => array(
                        'Ctx.php'     => "f",
                        'IetfCtx.php' => "f",
                    ),
                    'Poly1305.php'   => "f",
                    'Salsa20.php'    => "f",
                    'Ed25519.php'    => "f",
                    'ChaCha20.php'   => "f",
                    'Poly1305'       => array('State.php' => "f"),
                    'BLAKE2b.php'    => "f",
                    'Curve25519'     => array(
                        'Fe.php' => "f",
                        'Ge'     => array(
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'Cached.php'  => "f",
                            'P3.php'      => "f",
                            'P2.php'      => "f",
                        ),
                        'H.php'  => "f",
                    ),
                    'HSalsa20.php'   => "f",
                    'HChaCha20.php'  => "f",
                    'Curve25519.php' => "f",
                ),
                'File.php'   => "f",
                'Crypto.php' => "f",
                'Compat.php' => "f",
            ),
            'composer.json'     => "f",
            'src'               => array(
                'Core'                => array(
                    'AES.php'          => "f",
                    'XChaCha20.php'    => "f",
                    'SipHash.php'      => "f",
                    'Util.php'         => "f",
                    'Ristretto255.php' => "f",
                    'AES'              => array(
                        'Expanded.php'    => "f",
                        'Block.php'       => "f",
                        'KeySchedule.php' => "f",
                    ),
                    'X25519.php'       => "f",
                    'Base64'           => array(
                        'UrlSafe.php'  => "f",
                        'Common.php'   => "f",
                        'Original.php' => "f",
                    ),
                    'ChaCha20'         => array(
                        'Ctx.php'     => "f",
                        'IetfCtx.php' => "f",
                    ),
                    'Poly1305.php'     => "f",
                    'Salsa20.php'      => "f",
                    'Ed25519.php'      => "f",
                    'ChaCha20.php'     => "f",
                    'Poly1305'         => array('State.php' => "f"),
                    'AEGIS128L.php'    => "f",
                    'XSalsa20.php'     => "f",
                    'SecretStream'     => array('State.php' => "f"),
                    'AEGIS256.php'     => "f",
                    'BLAKE2b.php'      => "f",
                    'Curve25519'       => array(
                        'Fe.php'    => "f",
                        'Ge'        => array(
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'Cached.php'  => "f",
                            'P3.php'      => "f",
                            'P2.php'      => "f",
                        ),
                        'README.md' => "f",
                        'H.php'     => "f",
                    ),
                    'AEGIS'            => array(
                        'State256.php'  => "f",
                        'State128L.php' => "f",
                    ),
                    'HSalsa20.php'     => "f",
                    'HChaCha20.php'    => "f",
                    'Curve25519.php'   => "f",
                ),
                'Crypto32.php'        => "f",
                'File.php'            => "f",
                'Crypto.php'          => "f",
                'PHP52'               => array('SplFixedArray.php' => "f"),
                'Core32'              => array(
                    'XChaCha20.php'  => "f",
                    'SipHash.php'    => "f",
                    'Util.php'       => "f",
                    'X25519.php'     => "f",
                    'ChaCha20'       => array(
                        'Ctx.php'     => "f",
                        'IetfCtx.php' => "f",
                    ),
                    'Poly1305.php'   => "f",
                    'Salsa20.php'    => "f",
                    'Ed25519.php'    => "f",
                    'ChaCha20.php'   => "f",
                    'Poly1305'       => array('State.php' => "f"),
                    'XSalsa20.php'   => "f",
                    'Int64.php'      => "f",
                    'SecretStream'   => array('State.php' => "f"),
                    'BLAKE2b.php'    => "f",
                    'Curve25519'     => array(
                        'Fe.php'    => "f",
                        'Ge'        => array(
                            'P1p1.php'    => "f",
                            'Precomp.php' => "f",
                            'Cached.php'  => "f",
                            'P3.php'      => "f",
                            'P2.php'      => "f",
                        ),
                        'README.md' => "f",
                        'H.php'     => "f",
                    ),
                    'HSalsa20.php'   => "f",
                    'Int32.php'      => "f",
                    'HChaCha20.php'  => "f",
                    'Curve25519.php' => "f",
                ),
                'SodiumException.php' => "f",
                'Compat.php'          => "f",
            ),
            'lib'               => array(
                'ristretto255.php'      => "f",
                'php72compat.php'       => "f",
                'php84compat.php'       => "f",
                'php84compat_const.php' => "f",
                'sodium_compat.php'     => "f",
                'php72compat_const.php' => "f",
                'stream-xchacha20.php'  => "f",
                'constants.php'         => "f",
                'namespaced.php'        => "f",
            ),
            'LICENSE'           => "f",
        ),
        'class-wp-dependencies.php'                      => "f",
        'class-wp-object-cache.php'                      => "f",
        'cache.php'                                      => "f",
        'capabilities.php'                               => "f",
        'class-wp-scripts.php'                           => "f",
        'class-wp-user-meta-session-tokens.php'          => "f",
        'class-wp-block-list.php'                        => "f",
        'class-wp-http-requests-response.php'            => "f",
        'spl-autoload-compat.php'                        => "f",
        'class-wp-http-encoding.php'                     => "f",
        'bookmark-template.php'                          => "f",
        'class-wp-customize-nav-menus.php'               => "f",
        'category.php'                                   => "f",
        'block-template-utils.php'                       => "f",
        'widgets'                                        => array(
            'class-wp-widget-rss.php'             => "f",
            'class-wp-widget-recent-comments.php' => "f",
            'class-wp-widget-media-audio.php'     => "f",
            'class-wp-widget-pages.php'           => "f",
            'class-wp-widget-links.php'           => "f",
            'class-wp-widget-search.php'          => "f",
            'class-wp-widget-media.php'           => "f",
            'class-wp-widget-media-video.php'     => "f",
            'class-wp-widget-media-image.php'     => "f",
            'class-wp-widget-block.php'           => "f",
            'class-wp-widget-archives.php'        => "f",
            'class-wp-widget-media-gallery.php'   => "f",
            'class-wp-nav-menu-widget.php'        => "f",
            'class-wp-widget-custom-html.php'     => "f",
            'class-wp-widget-meta.php'            => "f",
            'class-wp-widget-recent-posts.php'    => "f",
            'class-wp-widget-calendar.php'        => "f",
            'class-wp-widget-tag-cloud.php'       => "f",
            'class-wp-widget-text.php'            => "f",
            'class-wp-widget-categories.php'      => "f",
        ),
        'js'                                             => array(
            'codemirror'                         => array(
                'htmlhint.js'        => "f",
                'jshint.js'          => "f",
                'csslint.js'         => "f",
                'codemirror.min.css' => "f",
                'jsonlint.js'        => "f",
                'codemirror.min.js'  => "f",
                'esprima.js'         => "f",
                'fakejshint.js'      => "f",
                'htmlhint-kses.js'   => "f",
            ),
            'quicktags.min.js'                   => "f",
            'wp-embed-template.min.js'           => "f",
            'wp-emoji.js'                        => "f",
            'hoverintent-js.min.js'              => "f",
            'media-models.js'                    => "f",
            'admin-bar.min.js'                   => "f",
            'wp-ajax-response.min.js'            => "f",
            'customize-views.min.js'             => "f",
            'swfupload'                          => array(
                'handlers.js'     => "f",
                'plugins'         => array(
                    'swfupload.swfobject.js' => "f",
                    'swfupload.queue.js'     => "f",
                    'swfupload.speed.js'     => "f",
                    'swfupload.cookies.js'   => "f",
                ),
                'swfupload.swf'   => "f",
                'handlers.min.js' => "f",
                'swfupload.js'    => "f",
                'license.txt'     => "f",
            ),
            'mce-view.js'                        => "f",
            'wpdialog.min.js'                    => "f",
            'customize-loader.min.js'            => "f",
            'utils.min.js'                       => "f",
            'twemoji.min.js'                     => "f",
            'wp-api.js'                          => "f",
            'wp-custom-header.js'                => "f",
            'shortcode.js'                       => "f",
            'admin-bar.js'                       => "f",
            'wp-sanitize.js'                     => "f",
            'media-models.min.js'                => "f",
            'zxcvbn.min.js'                      => "f",
            'customize-models.js'                => "f",
            'wp-pointer.js'                      => "f",
            'customize-loader.js'                => "f",
            'media-grid.min.js'                  => "f",
            'customize-preview-widgets.min.js'   => "f",
            'twemoji.js'                         => "f",
            'wp-auth-check.js'                   => "f",
            'zxcvbn-async.min.js'                => "f",
            'media-audiovideo.js'                => "f",
            'quicktags.js'                       => "f",
            'media-editor.min.js'                => "f",
            'json2.min.js'                       => "f",
            'hoverIntent.js'                     => "f",
            'customize-views.js'                 => "f",
            'wp-backbone.min.js'                 => "f",
            'customize-preview.min.js'           => "f",
            'api-request.min.js'                 => "f",
            'wp-embed-template.js'               => "f",
            'wplink.min.js'                      => "f",
            'heartbeat.min.js'                   => "f",
            'tw-sack.js'                         => "f",
            'dist'                               => array(
                'patterns.js'                               => "f",
                'undo-manager.js'                           => "f",
                'data-controls.min.js'                      => "f",
                'rich-text.min.js'                          => "f",
                'redux-routine.min.js'                      => "f",
                'url.js'                                    => "f",
                'viewport.min.js'                           => "f",
                'server-side-render.js'                     => "f",
                'editor.js'                                 => "f",
                'shortcode.js'                              => "f",
                'style-engine.min.js'                       => "f",
                'data.js'                                   => "f",
                'keycodes.js'                               => "f",
                'a11y.min.js'                               => "f",
                'components.min.js'                         => "f",
                'edit-widgets.min.js'                       => "f",
                'date.min.js'                               => "f",
                'is-shallow-equal.js'                       => "f",
                'wordcount.min.js'                          => "f",
                'interactivity-router.asset.php'            => "f",
                'style-engine.js'                           => "f",
                'nux.min.js'                                => "f",
                'fields.js'                                 => "f",
                'commands.min.js'                           => "f",
                'deprecated.min.js'                         => "f",
                'deprecated.js'                             => "f",
                'vendor'                                    => array(
                    'wp-polyfill-node-contains.min.js'     => "f",
                    'wp-polyfill-importmap.js'             => "f",
                    'react-jsx-runtime.js'                 => "f",
                    'lodash.js'                            => "f",
                    'wp-polyfill-element-closest.min.js'   => "f",
                    'wp-polyfill-object-fit.js'            => "f",
                    'react.js'                             => "f",
                    'wp-polyfill.min.js'                   => "f",
                    'wp-polyfill-dom-rect.min.js'          => "f",
                    'moment.min.js'                        => "f",
                    'wp-polyfill-inert.min.js'             => "f",
                    'regenerator-runtime.min.js'           => "f",
                    'wp-polyfill-url.js'                   => "f",
                    'wp-polyfill-dom-rect.js'              => "f",
                    'wp-polyfill-formdata.min.js'          => "f",
                    'react-dom.min.js'                     => "f",
                    'react.min.js'                         => "f",
                    'react-dom.min.js.LICENSE.txt'         => "f",
                    'react.min.js.LICENSE.txt'             => "f",
                    'regenerator-runtime.js'               => "f",
                    'moment.js'                            => "f",
                    'wp-polyfill-fetch.min.js'             => "f",
                    'wp-polyfill-element-closest.js'       => "f",
                    'lodash.min.js'                        => "f",
                    'react-dom.js'                         => "f",
                    'wp-polyfill-object-fit.min.js'        => "f",
                    'wp-polyfill-inert.js'                 => "f",
                    'wp-polyfill.js'                       => "f",
                    'wp-polyfill-fetch.js'                 => "f",
                    'wp-polyfill-url.min.js'               => "f",
                    'wp-polyfill-importmap.min.js'         => "f",
                    'react-jsx-runtime.min.js.LICENSE.txt' => "f",
                    'react-jsx-runtime.min.js'             => "f",
                    'wp-polyfill-formdata.js'              => "f",
                    'wp-polyfill-node-contains.js'         => "f",
                ),
                'html-entities.js'                          => "f",
                'interactivity-router.js'                   => "f",
                'reusable-blocks.min.js'                    => "f",
                'customize-widgets.js'                      => "f",
                'preferences.js'                            => "f",
                'list-reusable-blocks.js'                   => "f",
                'blocks.min.js'                             => "f",
                'token-list.js'                             => "f",
                'components.js'                             => "f",
                'interactivity-router.min.asset.php'        => "f",
                'block-serialization-default-parser.js'     => "f",
                'element.min.js'                            => "f",
                'element.js'                                => "f",
                'shortcode.min.js'                          => "f",
                'primitives.min.js'                         => "f",
                'hooks.js'                                  => "f",
                'nux.js'                                    => "f",
                'blob.min.js'                               => "f",
                'block-library.min.js'                      => "f",
                'edit-post.js'                              => "f",
                'preferences-persistence.js'                => "f",
                'format-library.js'                         => "f",
                'edit-site.js'                              => "f",
                'i18n.min.js'                               => "f",
                'token-list.min.js'                         => "f",
                'private-apis.min.js'                       => "f",
                'keyboard-shortcuts.js'                     => "f",
                'interactivity.js'                          => "f",
                'annotations.js'                            => "f",
                'blocks.js'                                 => "f",
                'patterns.min.js'                           => "f",
                'priority-queue.min.js'                     => "f",
                'api-fetch.min.js'                          => "f",
                'warning.js'                                => "f",
                'undo-manager.min.js'                       => "f",
                'editor.min.js'                             => "f",
                'html-entities.min.js'                      => "f",
                'format-library.min.js'                     => "f",
                'date.js'                                   => "f",
                'preferences.min.js'                        => "f",
                'media-utils.min.js'                        => "f",
                'private-apis.js'                           => "f",
                'dom.js'                                    => "f",
                'annotations.min.js'                        => "f",
                'keyboard-shortcuts.min.js'                 => "f",
                'edit-widgets.js'                           => "f",
                'warning.min.js'                            => "f",
                'block-editor.min.js'                       => "f",
                'edit-post.min.js'                          => "f",
                'a11y.js'                                   => "f",
                'edit-site.min.js'                          => "f",
                'dom-ready.min.js'                          => "f",
                'fields.min.js'                             => "f",
                'api-fetch.js'                              => "f",
                'url.min.js'                                => "f",
                'reusable-blocks.js'                        => "f",
                'keycodes.min.js'                           => "f",
                'block-library.js'                          => "f",
                'development'                               => array(
                    'react-refresh-runtime.js'     => "f",
                    'react-refresh-entry.js'       => "f",
                    'react-refresh-runtime.min.js' => "f",
                    'react-refresh-entry.min.js'   => "f",
                ),
                'hooks.min.js'                              => "f",
                'widgets.js'                                => "f",
                'escape-html.js'                            => "f",
                'compose.js'                                => "f",
                'script-modules'                            => array(
                    'block-library'        => array(
                        'query'      => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'image'      => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'search'     => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'file'       => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                        'navigation' => array(
                            'view.min.js' => "f",
                            'view.js'     => "f",
                        ),
                    ),
                    'interactivity-router' => array(
                        'index.min.js' => "f",
                        'index.js'     => "f",
                    ),
                    'interactivity'        => array(
                        'index.min.js' => "f",
                        'index.js'     => "f",
                        'debug.js'     => "f",
                        'debug.min.js' => "f",
                    ),
                    'a11y'                 => array(
                        'index.min.js' => "f",
                        'index.js'     => "f",
                    ),
                ),
                'core-commands.min.js'                      => "f",
                'is-shallow-equal.min.js'                   => "f",
                'block-directory.js'                        => "f",
                'media-utils.js'                            => "f",
                'interactivity.min.js'                      => "f",
                'viewport.js'                               => "f",
                'preferences-persistence.min.js'            => "f",
                'rich-text.js'                              => "f",
                'compose.min.js'                            => "f",
                'notices.min.js'                            => "f",
                'blob.js'                                   => "f",
                'wordcount.js'                              => "f",
                'data.min.js'                               => "f",
                'customize-widgets.min.js'                  => "f",
                'core-data.min.js'                          => "f",
                'block-directory.min.js'                    => "f",
                'router.min.js'                             => "f",
                'autop.js'                                  => "f",
                'dom-ready.js'                              => "f",
                'priority-queue.js'                         => "f",
                'core-data.js'                              => "f",
                'block-editor.js'                           => "f",
                'list-reusable-blocks.min.js'               => "f",
                'dom.min.js'                                => "f",
                'core-commands.js'                          => "f",
                'block-serialization-default-parser.min.js' => "f",
                'notices.js'                                => "f",
                'plugins.js'                                => "f",
                'widgets.min.js'                            => "f",
                'data-controls.js'                          => "f",
                'commands.js'                               => "f",
                'i18n.js'                                   => "f",
                'interactivity-router.min.js'               => "f",
                'router.js'                                 => "f",
                'plugins.min.js'                            => "f",
                'escape-html.min.js'                        => "f",
                'primitives.js'                             => "f",
                'server-side-render.min.js'                 => "f",
                'redux-routine.js'                          => "f",
                'autop.min.js'                              => "f",
            ),
            'plupload'                           => array(
                'handlers.js'              => "f",
                'plupload.min.js'          => "f",
                'plupload.full.min.js'     => "f",
                'handlers.min.js'          => "f",
                'plupload.js'              => "f",
                'wp-plupload.min.js'       => "f",
                'wp-plupload.js'           => "f",
                'moxie.js'                 => "f",
                'plupload.flash.swf'       => "f",
                'plupload.silverlight.xap' => "f",
                'moxie.min.js'             => "f",
                'license.txt'              => "f",
            ),
            'wp-emoji-loader.js'                 => "f",
            'wp-a11y.js'                         => "f",
            'backbone.min.js'                    => "f",
            'mediaelement'                       => array(
                'wp-playlist.js'                    => "f",
                'wp-mediaelement.min.css'           => "f",
                'bigplay.svg'                       => "f",
                'mediaelement-and-player.min.js'    => "f",
                'wp-mediaelement.css'               => "f",
                'wp-mediaelement.js'                => "f",
                'mediaelementplayer.css'            => "f",
                'mediaelement.js'                   => "f",
                'jumpforward.png'                   => "f",
                'loading.gif'                       => "f",
                'wp-mediaelement.min.js'            => "f",
                'skipback.png'                      => "f",
                'controls.svg'                      => "f",
                'mediaelementplayer-legacy.min.css' => "f",
                'froogaloop.min.js'                 => "f",
                'wp-playlist.min.js'                => "f",
                'renderers'                         => array(
                    'vimeo.min.js' => "f",
                    'vimeo.js'     => "f",
                ),
                'mediaelementplayer-legacy.css'     => "f",
                'bigplay.png'                       => "f",
                'mediaelement.min.js'               => "f",
                'background.png'                    => "f",
                'mejs-controls.svg'                 => "f",
                'mejs-controls.png'                 => "f",
                'mediaelementplayer.min.css'        => "f",
                'mediaelement-migrate.min.js'       => "f",
                'mediaelement-and-player.js'        => "f",
                'controls.png'                      => "f",
                'mediaelement-migrate.js'           => "f",
            ),
            'customize-base.js'                  => "f",
            'wp-ajax-response.js'                => "f",
            'shortcode.min.js'                   => "f",
            'tw-sack.min.js'                     => "f",
            'customize-models.min.js'            => "f",
            'clipboard.min.js'                   => "f",
            'wp-emoji-release.min.js'            => "f",
            'wp-a11y.min.js'                     => "f",
            'wp-auth-check.min.js'               => "f",
            'jcrop'                              => array(
                'Jcrop.gif'            => "f",
                'jquery.Jcrop.min.css' => "f",
                'jquery.Jcrop.min.js'  => "f",
            ),
            'zxcvbn-async.js'                    => "f",
            'wp-lists.js'                        => "f",
            'api-request.js'                     => "f",
            'swfobject.js'                       => "f",
            'wp-util.min.js'                     => "f",
            'wp-emoji.min.js'                    => "f",
            'json2.js'                           => "f",
            'backbone.js'                        => "f",
            'crop'                               => array(
                'cropper.css'      => "f",
                'marqueeHoriz.gif' => "f",
                'marqueeVert.gif'  => "f",
                'cropper.js'       => "f",
            ),
            'wp-backbone.js'                     => "f",
            'wp-lists.min.js'                    => "f",
            'customize-selective-refresh.js'     => "f",
            'colorpicker.min.js'                 => "f",
            'heartbeat.js'                       => "f",
            'wp-embed.min.js'                    => "f",
            'wp-emoji-loader.min.js'             => "f",
            'customize-preview-nav-menus.min.js' => "f",
            'wp-pointer.min.js'                  => "f",
            'wp-api.min.js'                      => "f",
            'autosave.min.js'                    => "f",
            'customize-selective-refresh.min.js' => "f",
            'underscore.js'                      => "f",
            'imgareaselect'                      => array(
                'border-anim-h.gif'           => "f",
                'jquery.imgareaselect.min.js' => "f",
                'imgareaselect.css'           => "f",
                'border-anim-v.gif'           => "f",
                'jquery.imgareaselect.js'     => "f",
            ),
            'utils.js'                           => "f",
            'media-views.min.js'                 => "f",
            'wp-custom-header.min.js'            => "f",
            'wp-list-revisions.js'               => "f",
            'customize-base.min.js'              => "f",
            'mce-view.min.js'                    => "f",
            'autosave.js'                        => "f",
            'wpdialog.js'                        => "f",
            'media-grid.js'                      => "f",
            'wp-sanitize.min.js'                 => "f",
            'underscore.min.js'                  => "f",
            'wp-list-revisions.min.js'           => "f",
            'hoverIntent.min.js'                 => "f",
            'customize-preview.js'               => "f",
            'comment-reply.min.js'               => "f",
            'colorpicker.js'                     => "f",
            'tinymce'                            => array(
                'tiny_mce_popup.js' => "f",
                'langs'             => array('wp-langs-en.js' => "f"),
                'skins'             => array(
                    'lightgray' => array(
                        'content.inline.min.css' => "f",
                        'img'                    => array(
                            'object.gif' => "f",
                            'trans.gif'  => "f",
                            'anchor.gif' => "f",
                            'loader.gif' => "f",
                        ),
                        'skin.min.css'           => "f",
                        'content.min.css'        => "f",
                        'fonts'                  => array(
                            'tinymce-small.json' => "f",
                            'tinymce-small.eot'  => "f",
                            'tinymce.woff'       => "f",
                            'readme.md'          => "f",
                            'tinymce-small.svg'  => "f",
                            'tinymce.json'       => "f",
                            'tinymce-small.woff' => "f",
                            'tinymce.svg'        => "f",
                            'tinymce.eot'        => "f",
                            'tinymce-small.ttf'  => "f",
                            'tinymce.ttf'        => "f",
                        ),
                        'skin.ie7.min.css'       => "f",
                    ),
                    'wordpress' => array(
                        'images'         => array(
                            'audio.png'           => "f",
                            'dashicon-edit.png'   => "f",
                            'dashicon-no-alt.png' => "f",
                            'gallery-2x.png'      => "f",
                            'embedded.png'        => "f",
                            'playlist-audio.png'  => "f",
                            'more.png'            => "f",
                            'pagebreak.png'       => "f",
                            'more-2x.png'         => "f",
                            'playlist-video.png'  => "f",
                            'pagebreak-2x.png'    => "f",
                            'gallery.png'         => "f",
                            'dashicon-no.png'     => "f",
                            'video.png'           => "f",
                        ),
                        'wp-content.css' => "f",
                    ),
                ),
                'themes'            => array(
                    'modern' => array(
                        'theme.js'     => "f",
                        'theme.min.js' => "f",
                    ),
                    'inlite' => array(
                        'theme.js'     => "f",
                        'theme.min.js' => "f",
                    ),
                ),
                'wp-tinymce.js.gz'  => "f",
                'wp-tinymce.js'     => "f",
                'plugins'           => array(
                    'fullscreen'     => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'directionality' => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpembed'        => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'paste'          => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'charmap'        => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'link'           => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'hr'             => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpgallery'      => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'lists'          => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpautoresize'   => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wordpress'      => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'compat3x'       => array(
                        'plugin.min.js' => "f",
                        'css'           => array('dialog.css' => "f"),
                        'plugin.js'     => "f",
                    ),
                    'wpdialogs'      => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'media'          => array(
                        'plugin.min.js'   => "f",
                        'moxieplayer.swf' => "f",
                        'plugin.js'       => "f",
                    ),
                    'image'          => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpeditimage'    => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'textcolor'      => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wplink'         => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wptextpattern'  => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'colorpicker'    => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpview'         => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'tabfocus'       => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpfullscreen'   => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                    'wpemoji'        => array(
                        'plugin.min.js' => "f",
                        'plugin.js'     => "f",
                    ),
                ),
                'utils'             => array(
                    'form_utils.js'       => "f",
                    'validate.js'         => "f",
                    'mctabs.js'           => "f",
                    'editable_selects.js' => "f",
                ),
                'wp-tinymce.php'    => "f",
                'license.txt'       => "f",
                'tinymce.min.js'    => "f",
                'wp-mce-help.php'   => "f",
            ),
            'wp-embed.js'                        => "f",
            'clipboard.js'                       => "f",
            'jquery'                             => array(
                'jquery.schedule.js'          => "f",
                'suggest.min.js'              => "f",
                'jquery.query.js'             => "f",
                'jquery.masonry.min.js'       => "f",
                'jquery.form.min.js'          => "f",
                'jquery.table-hotkeys.min.js' => "f",
                'jquery.hotkeys.js'           => "f",
                'jquery-migrate.min.js'       => "f",
                'jquery.ui.touch-punch.js'    => "f",
                'jquery.serialize-object.js'  => "f",
                'ui'                          => array(
                    'slider.js'                         => "f",
                    'effect-clip.min.js'                => "f",
                    'effect-fold.min.js'                => "f",
                    'spinner.min.js'                    => "f",
                    'effect-explode.min.js'             => "f",
                    'draggable.js'                      => "f",
                    'progressbar.js'                    => "f",
                    'sortable.min.js'                   => "f",
                    'progressbar.min.js'                => "f",
                    'effect-puff.js'                    => "f",
                    'jquery.ui.effect-highlight.min.js' => "f",
                    'jquery.ui.button.min.js'           => "f",
                    'selectable.min.js'                 => "f",
                    'menu.min.js'                       => "f",
                    'jquery.ui.effect-shake.min.js'     => "f",
                    'effect-bounce.js'                  => "f",
                    'dialog.js'                         => "f",
                    'jquery.ui.mouse.min.js'            => "f",
                    'effect-size.min.js'                => "f",
                    'selectable.js'                     => "f",
                    'effect-size.js'                    => "f",
                    'droppable.min.js'                  => "f",
                    'jquery.ui.effect-scale.min.js'     => "f",
                    'datepicker.js'                     => "f",
                    'selectmenu.js'                     => "f",
                    'checkboxradio.min.js'              => "f",
                    'droppable.js'                      => "f",
                    'jquery.ui.slider.min.js'           => "f",
                    'menu.js'                           => "f",
                    'tooltip.min.js'                    => "f",
                    'jquery.ui.tabs.min.js'             => "f",
                    'accordion.js'                      => "f",
                    'jquery.ui.spinner.min.js'          => "f",
                    'jquery.ui.accordion.min.js'        => "f",
                    'effect-shake.js'                   => "f",
                    'position.min.js'                   => "f",
                    'mouse.min.js'                      => "f",
                    'effect-transfer.js'                => "f",
                    'effect-highlight.min.js'           => "f",
                    'resizable.js'                      => "f",
                    'jquery.ui.sortable.min.js'         => "f",
                    'jquery.ui.resizable.min.js'        => "f",
                    'button.min.js'                     => "f",
                    'jquery.ui.core.min.js'             => "f",
                    'jquery.ui.menu.min.js'             => "f",
                    'effect-highlight.js'               => "f",
                    'effect-shake.min.js'               => "f",
                    'jquery.ui.datepicker.min.js'       => "f",
                    'jquery.ui.autocomplete.min.js'     => "f",
                    'effect-puff.min.js'                => "f",
                    'effect-pulsate.min.js'             => "f",
                    'tooltip.js'                        => "f",
                    'button.js'                         => "f",
                    'accordion.min.js'                  => "f",
                    'effect.min.js'                     => "f",
                    'effect-fold.js'                    => "f",
                    'controlgroup.js'                   => "f",
                    'effect-explode.js'                 => "f",
                    'widget.min.js'                     => "f",
                    'effect-scale.js'                   => "f",
                    'effect-fade.min.js'                => "f",
                    'effect-slide.js'                   => "f",
                    'datepicker.min.js'                 => "f",
                    'checkboxradio.js'                  => "f",
                    'effect-pulsate.js'                 => "f",
                    'jquery.ui.effect-fold.min.js'      => "f",
                    'tabs.js'                           => "f",
                    'effect.js'                         => "f",
                    'autocomplete.js'                   => "f",
                    'autocomplete.min.js'               => "f",
                    'jquery.ui.effect-pulsate.min.js'   => "f",
                    'effect-clip.js'                    => "f",
                    'jquery.ui.effect-slide.min.js'     => "f",
                    'effect-blind.js'                   => "f",
                    'core.js'                           => "f",
                    'selectmenu.min.js'                 => "f",
                    'jquery.ui.draggable.min.js'        => "f",
                    'tabs.min.js'                       => "f",
                    'effect-drop.min.js'                => "f",
                    'draggable.min.js'                  => "f",
                    'jquery.ui.effect-drop.min.js'      => "f",
                    'dialog.min.js'                     => "f",
                    'jquery.ui.progressbar.min.js'      => "f",
                    'jquery.ui.tooltip.min.js'          => "f",
                    'jquery.ui.effect-bounce.min.js'    => "f",
                    'jquery.ui.effect-blind.min.js'     => "f",
                    'jquery.ui.effect-clip.min.js'      => "f",
                    'effect-transfer.min.js'            => "f",
                    'slider.min.js'                     => "f",
                    'resizable.min.js'                  => "f",
                    'sortable.js'                       => "f",
                    'jquery.ui.effect-fade.min.js'      => "f",
                    'effect-scale.min.js'               => "f",
                    'core.min.js'                       => "f",
                    'jquery.ui.effect-transfer.min.js'  => "f",
                    'jquery.ui.position.min.js'         => "f",
                    'spinner.js'                        => "f",
                    'effect-bounce.min.js'              => "f",
                    'effect-fade.js'                    => "f",
                    'effect-blind.min.js'               => "f",
                    'controlgroup.min.js'               => "f",
                    'effect-slide.min.js'               => "f",
                    'effect-drop.js'                    => "f",
                    'jquery.ui.selectable.min.js'       => "f",
                    'jquery.ui.dialog.min.js'           => "f",
                    'jquery.ui.effect.min.js'           => "f",
                    'jquery.ui.effect-explode.min.js'   => "f",
                    'mouse.js'                          => "f",
                    'jquery.ui.droppable.min.js'        => "f",
                    'jquery.ui.widget.min.js'           => "f",
                ),
                'suggest.js'                  => "f",
                'jquery.js'                   => "f",
                'jquery.hotkeys.min.js'       => "f",
                'jquery.color.min.js'         => "f",
                'jquery.form.js'              => "f",
                'jquery-migrate.js'           => "f",
                'jquery.table-hotkeys.js'     => "f",
                'jquery.min.js'               => "f",
            ),
            'masonry.min.js'                     => "f",
            'media-editor.js'                    => "f",
            'wp-util.js'                         => "f",
            'imagesloaded.min.js'                => "f",
            'thickbox'                           => array(
                'macFFBgHack.png'      => "f",
                'thickbox.js'          => "f",
                'loadingAnimation.gif' => "f",
                'thickbox.css'         => "f",
            ),
            'media-audiovideo.min.js'            => "f",
            'media-views.js'                     => "f",
            'customize-preview-widgets.js'       => "f",
            'wplink.js'                          => "f",
            'customize-preview-nav-menus.js'     => "f",
            'comment-reply.js'                   => "f",
        ),
        'https-detection.php'                            => "f",
        'class-wp-text-diff-renderer-inline.php'         => "f",
        'robots-template.php'                            => "f",
        'block-supports'                                 => array(
            'custom-classname.php'       => "f",
            'generated-classname.php'    => "f",
            'settings.php'               => "f",
            'utils.php'                  => "f",
            'shadow.php'                 => "f",
            'align.php'                  => "f",
            'background.php'             => "f",
            'typography.php'             => "f",
            'block-style-variations.php' => "f",
            'duotone.php'                => "f",
            'dimensions.php'             => "f",
            'border.php'                 => "f",
            'elements.php'               => "f",
            'colors.php'                 => "f",
            'spacing.php'                => "f",
            'position.php'               => "f",
            'layout.php'                 => "f",
        ),
        'functions.wp-scripts.php'                       => "f",
        'deprecated.php'                                 => "f",
        'link-template.php'                              => "f",
        'cache-compat.php'                               => "f",
        'theme-compat'                                   => array(
            'header-embed.php'   => "f",
            'embed-content.php'  => "f",
            'embed-404.php'      => "f",
            'footer.php'         => "f",
            'sidebar.php'        => "f",
            'comments-popup.php' => "f",
            'footer-embed.php'   => "f",
            'comments.php'       => "f",
            'embed.php'          => "f",
            'header.php'         => "f",
        ),
        'class-wp-block-metadata-registry.php'           => "f",
        'class-wp-token-map.php'                         => "f",
        'style-engine'                                   => array(
            'class-wp-style-engine.php'                  => "f",
            'class-wp-style-engine-css-rule.php'         => "f",
            'class-wp-style-engine-css-declarations.php' => "f",
            'class-wp-style-engine-processor.php'        => "f",
            'class-wp-style-engine-css-rules-store.php'  => "f",
        ),
        'embed.php'                                      => "f",
        'class-wp-editor.php'                            => "f",
        'load.php'                                       => "f",
        'class-wp-block-type.php'                        => "f",
        'widgets.php'                                    => "f",
        'class-wp-image-editor.php'                      => "f",
        'class-wp-oembed-controller.php'                 => "f",
        'blocks'                                         => array(
            'navigation.php'                   => "f",
            'widget-group'                     => array('block.json' => "f"),
            'freeform'                         => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'button.php'                       => "f",
            'home-link'                        => array('block.json' => "f"),
            'categories'                       => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'post-author'                      => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'comments-pagination-numbers.php'  => "f",
            'cover'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'footnotes'                        => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'loginout'                         => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'navigation-submenu.php'           => "f",
            'social-link.php'                  => "f",
            'home-link.php'                    => "f",
            'html'                             => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'page-list'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'comments-pagination-previous.php' => "f",
            'rss.php'                          => "f",
            'media-text.php'                   => "f",
            'page-list-item'                   => array('block.json' => "f"),
            'post-comments'                    => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'read-more'                        => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'list.php'                         => "f",
            'post-navigation-link.php'         => "f",
            'comment-date'                     => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'comment-reply-link'               => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'shortcode.php'                    => "f",
            'post-author.php'                  => "f",
            'code'                             => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'read-more.php'                    => "f",
            'nextpage'                         => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'post-terms'                       => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'paragraph'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'legacy-widget.php'                => "f",
            'legacy-widget'                    => array('block.json' => "f"),
            'post-terms.php'                   => "f",
            'columns'                          => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'archives.php'                     => "f",
            'text-columns'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'more'                             => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'comments-pagination-next'         => array('block.json' => "f"),
            'post-featured-image'              => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'widget-group.php'                 => "f",
            'require-dynamic-blocks.php'       => "f",
            'calendar'                         => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'separator'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'query'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'view.min.js'        => "f",
                'block.json'         => "f",
                'view.asset.php'     => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'view.min.asset.php' => "f",
                'style-rtl.min.css'  => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
            ),
            'tag-cloud.php'                    => "f",
            'post-content'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'comments-pagination-numbers'      => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'comments'                         => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'navigation-submenu'               => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'comments-pagination-previous'     => array('block.json' => "f"),
            'classic'                          => array('block.json' => "f"),
            'latest-comments.php'              => "f",
            'post-title.php'                   => "f",
            'post-author-biography.php'        => "f",
            'post-title'                       => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'avatar.php'                       => "f",
            'comments-query-loop'              => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'embed'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'image.php'                        => "f",
            'buttons'                          => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'comment-content.php'              => "f",
            'post-comments-form'               => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'cover.php'                        => "f",
            'template-part'                    => array(
                'editor.min.css'     => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'theme.css'          => "f",
            ),
            'archives'                         => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'latest-posts.php'                 => "f",
            'button'                           => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'comment-template.php'             => "f",
            'site-logo'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'post-featured-image.php'          => "f",
            'query-pagination-next'            => array('block.json' => "f"),
            'index.php'                        => "f",
            'list-item'                        => array('block.json' => "f"),
            'site-title'                       => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'gallery.php'                      => "f",
            'quote'                            => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'theme-rtl.css'     => "f",
                'theme.min.css'     => "f",
                'block.json'        => "f",
                'theme-rtl.min.css' => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
                'theme.css'         => "f",
            ),
            'image'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'view.min.js'        => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'view.asset.php'     => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'view.min.asset.php' => "f",
                'style-rtl.min.css'  => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'comment-author-name.php'          => "f",
            'comment-template'                 => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'comment-edit-link.php'            => "f",
            'post-excerpt'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'audio'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'social-link'                      => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'site-title.php'                   => "f",
            'preformatted'                     => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'table'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'avatar'                           => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'subhead'                          => array('block.json' => "f"),
            'rss'                              => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'heading.php'                      => "f",
            'post-content.php'                 => "f",
            'post-comments.php'                => "f",
            'comment-reply-link.php'           => "f",
            'post-template'                    => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'post-excerpt.php'                 => "f",
            'spacer'                           => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'page-list.php'                    => "f",
            'comment-author-name'              => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'term-description'                 => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'query-title.php'                  => "f",
            'block.php'                        => "f",
            'query.php'                        => "f",
            'latest-comments'                  => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'comments-title.php'               => "f",
            'comments.php'                     => "f",
            'video'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'post-navigation-link'             => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'search'                           => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'view.min.js'        => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'view.asset.php'     => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'view.min.asset.php' => "f",
                'style-rtl.min.css'  => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'social-links'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'post-date.php'                    => "f",
            'term-description.php'             => "f",
            'query-pagination-previous.php'    => "f",
            'navigation-link'                  => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'post-comments-form.php'           => "f",
            'footnotes.php'                    => "f",
            'site-tagline.php'                 => "f",
            'missing'                          => array('block.json' => "f"),
            'comments-pagination.php'          => "f",
            'page-list-item.php'               => "f",
            'post-template.php'                => "f",
            'query-pagination-next.php'        => "f",
            'pattern'                          => array('block.json' => "f"),
            'comments-pagination-next.php'     => "f",
            'tag-cloud'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'query-no-results.php'             => "f",
            'comments-title'                   => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'block'                            => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'column'                           => array('block.json' => "f"),
            'gallery'                          => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'comment-edit-link'                => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'site-tagline'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'shortcode'                        => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'verse'                            => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'comments-pagination'              => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'list'                             => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'site-logo.php'                    => "f",
            'file'                             => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'view.min.js'        => "f",
                'block.json'         => "f",
                'view.asset.php'     => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'view.min.asset.php' => "f",
                'style-rtl.min.css'  => "f",
                'view.js'            => "f",
                'style.min.css'      => "f",
            ),
            'blocks-json.php'                  => "f",
            'latest-posts'                     => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'template-part.php'                => "f",
            'query-pagination-numbers'         => array(
                'editor.min.css'     => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
            ),
            'query-pagination-previous'        => array('block.json' => "f"),
            'post-author-name'                 => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'navigation'                       => array(
                'style.css'                => "f",
                'editor.min.css'           => "f",
                'style-rtl.css'            => "f",
                'view-modal.min.js'        => "f",
                'view.min.js'              => "f",
                'view-modal.js'            => "f",
                'block.json'               => "f",
                'view-modal.min.asset.php' => "f",
                'view.asset.php'           => "f",
                'editor-rtl.min.css'       => "f",
                'editor.css'               => "f",
                'editor-rtl.css'           => "f",
                'view.min.asset.php'       => "f",
                'style-rtl.min.css'        => "f",
                'view.js'                  => "f",
                'style.min.css'            => "f",
                'view-modal.asset.php'     => "f",
            ),
            'heading'                          => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'query-pagination-numbers.php'     => "f",
            'require-static-blocks.php'        => "f",
            'group'                            => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'navigation-link.php'              => "f",
            'details'                          => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'pullquote'                        => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'theme-rtl.css'      => "f",
                'theme.min.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'theme-rtl.min.css'  => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
                'theme.css'          => "f",
            ),
            'query-title'                      => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'query-no-results'                 => array('block.json' => "f"),
            'query-pagination'                 => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
            'query-pagination.php'             => "f",
            'pattern.php'                      => "f",
            'post-author-biography'            => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'file.php'                         => "f",
            'categories.php'                   => "f",
            'search.php'                       => "f",
            'post-date'                        => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'calendar.php'                     => "f",
            'comment-content'                  => array(
                'style.css'         => "f",
                'style-rtl.css'     => "f",
                'block.json'        => "f",
                'style-rtl.min.css' => "f",
                'style.min.css'     => "f",
            ),
            'comment-date.php'                 => "f",
            'post-author-name.php'             => "f",
            'loginout.php'                     => "f",
            'media-text'                       => array(
                'style.css'          => "f",
                'editor.min.css'     => "f",
                'style-rtl.css'      => "f",
                'block.json'         => "f",
                'editor-rtl.min.css' => "f",
                'editor.css'         => "f",
                'editor-rtl.css'     => "f",
                'style-rtl.min.css'  => "f",
                'style.min.css'      => "f",
            ),
        ),
        'block-patterns'                                 => array(
            'three-buttons.php'                        => "f",
            'query-offset-posts.php'                   => "f",
            'query-standard-posts.php'                 => "f",
            'two-images.php'                           => "f",
            'query-grid-posts.php'                     => "f",
            'text-two-columns.php'                     => "f",
            'query-medium-posts.php'                   => "f",
            'text-three-columns-buttons.php'           => "f",
            'heading-paragraph.php'                    => "f",
            'query-large-title-posts.php'              => "f",
            'large-header.php'                         => "f",
            'text-two-columns-with-images.php'         => "f",
            'social-links-shared-background-color.php' => "f",
            'large-header-button.php'                  => "f",
            'two-buttons.php'                          => "f",
            'query-small-posts.php'                    => "f",
            'quote.php'                                => "f",
        ),
        'cron.php'                                       => "f",
        'ms-site.php'                                    => "f",
        'class-walker-page-dropdown.php'                 => "f",
        'images'                                         => array(
            'crystal'                   => array(
                'audio.png'       => "f",
                'archive.png'     => "f",
                'document.png'    => "f",
                'text.png'        => "f",
                'default.png'     => "f",
                'code.png'        => "f",
                'spreadsheet.png' => "f",
                'interactive.png' => "f",
                'license.txt'     => "f",
                'video.png'       => "f",
            ),
            'w-logo-blue-white-bg.png'  => "f",
            'wpspin.gif'                => "f",
            'blank.gif'                 => "f",
            'rss.png'                   => "f",
            'uploader-icons-2x.png'     => "f",
            'arrow-pointer-blue-2x.png' => "f",
            'icon-pointer-flag-2x.png'  => "f",
            'toggle-arrow-2x.png'       => "f",
            'smilies'                   => array(
                'icon_smile.gif'     => "f",
                'mrgreen.png'        => "f",
                'icon_mad.gif'       => "f",
                'icon_neutral.gif'   => "f",
                'icon_redface.gif'   => "f",
                'icon_cry.gif'       => "f",
                'icon_question.gif'  => "f",
                'icon_surprised.gif' => "f",
                'icon_biggrin.gif'   => "f",
                'icon_twisted.gif'   => "f",
                'icon_razz.gif'      => "f",
                'simple-smile.png'   => "f",
                'rolleyes.png'       => "f",
                'icon_lol.gif'       => "f",
                'icon_cool.gif'      => "f",
                'icon_mrgreen.gif'   => "f",
                'icon_eek.gif'       => "f",
                'icon_idea.gif'      => "f",
                'icon_sad.gif'       => "f",
                'icon_confused.gif'  => "f",
                'icon_evil.gif'      => "f",
                'icon_exclaim.gif'   => "f",
                'frownie.png'        => "f",
                'icon_arrow.gif'     => "f",
                'icon_wink.gif'      => "f",
                'icon_rolleyes.gif'  => "f",
            ),
            'media'                     => array(
                'default.svg'     => "f",
                'audio.png'       => "f",
                'video.svg'       => "f",
                'interactive.svg' => "f",
                'archive.png'     => "f",
                'audio.svg'       => "f",
                'document.svg'    => "f",
                'document.png'    => "f",
                'archive.svg'     => "f",
                'text.png'        => "f",
                'default.png'     => "f",
                'code.png'        => "f",
                'spreadsheet.png' => "f",
                'text.svg'        => "f",
                'interactive.png' => "f",
                'code.svg'        => "f",
                'spreadsheet.svg' => "f",
                'video.png'       => "f",
            ),
            'down_arrow-2x.gif'         => "f",
            'wpicons-2x.png'            => "f",
            'wlw'                       => array(
                'wp-comments.png'  => "f",
                'wp-icon.png'      => "f",
                'wp-watermark.png' => "f",
            ),
            'down_arrow.gif'            => "f",
            'arrow-pointer-blue.png'    => "f",
            'xit.gif'                   => "f",
            'spinner.gif'               => "f",
            'rss-2x.png'                => "f",
            'admin-bar-sprite-2x.png'   => "f",
            'admin-bar-sprite.png'      => "f",
            'wpspin-2x.gif'             => "f",
            'spinner-2x.gif'            => "f",
            'uploader-icons.png'        => "f",
            'wpicons.png'               => "f",
            'icon-pointer-flag.png'     => "f",
            'xit-2x.gif'                => "f",
            'w-logo-blue.png'           => "f",
            'toggle-arrow.png'          => "f",
        ),
        'class-wp-application-passwords.php'             => "f",
        'class-wp-oembed.php'                            => "f",
        'rewrite.php'                                    => "f",
        'theme-templates.php'                            => "f",
        'l10n.php'                                       => "f",
        'class-wp-script-modules.php'                    => "f",
    ),
    'wp-load.php'          => "f",
    'wp-config-sample.php' => "f",
    'wp-activate.php'      => "f",
    'wp-mail.php'          => "f",
    'license.txt'          => "f",
);
