<?php
require_once 'wp-config.php';

global $wpdb;
$table_name = $wpdb->prefix . 'huong_dan_pages';

// Update icons for specific pages
$updates = [
    'sepay-la-gi' => 'fas fa-info-circle',
    'goi-theo-diem-ban' => 'fas fa-store',
    'dang-ky-sepay' => 'fas fa-user-plus',
    'them-tai-khoan-ngan-hang' => 'fas fa-university',
    'xem-giao-dich' => 'fas fa-list',
    'nguoi-dung-phan-quyen' => 'fas fa-users',
    'tai-khoan-phu' => 'fas fa-user',
    'cau-hinh-tk-ngan-hang' => 'fas fa-cog',
    'goi-dich-vu' => 'fas fa-box',
    'hoa-don-thanh-toan' => 'fas fa-receipt',
    'cau-hinh-chung' => 'fas fa-tools',
    'tich-hop-telegram' => 'fab fa-telegram-plane',
    'tich-hop-lark-messenger' => 'fas fa-comment',
    'tich-hop-viber' => 'fab fa-viber',
    'mobile-app' => 'fas fa-mobile-alt',
    'tich-hop-loa-thanh-toan' => 'fas fa-volume-up'
];

echo "Starting icon updates...\n";

foreach ($updates as $slug => $icon) {
    $result = $wpdb->update(
        $table_name,
        ['icon' => $icon],
        ['slug' => $slug],
        ['%s'],
        ['%s']
    );
    echo "Updated $slug with icon $icon: " . ($result !== false ? 'Success' : 'Failed') . "\n";
}

echo "Icon update completed!\n";
?>
